package org.nonamespace.word.server.facade;

import org.nonamespace.word.server.domain.Textbook;

import java.util.List;

public interface TextbookManagerService {
    void fixWordMeanings();

    /**
     * 导入教材
     */
    void importTextbook();

    /**
     * 教材入库
     *
     * @param textbook 教材对象
     * @return textbookId 教材id
     */
    String storeTextbook(Textbook textbook);

    void initTextbookBaseInfo(String jsonStr);


    /**
     * 生成音频ZIP文件并上传到OSS
     *
     * @param textbookItemIds 教材项ID列表
     * @param textbook 教材
     * @return OSS下载链接
     */
    String generateAudioZipAndUpload(List<String> textbookItemIds, Textbook textbook);

    /**
     * 生成练习PDF文件
     *
     * @param textbookItemIds 教材项ID列表
     * @param textbook 教材
     * @param pdfName PDF文件名
     * @return PDF字节流
     */
    java.io.ByteArrayOutputStream generateExercisePdf(List<String> textbookItemIds, Textbook textbook, String pdfName);
}
