package org.nonamespace.word.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.common.utils.ThreadPoolService;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.*;
import org.nonamespace.word.server.dto.course.*;
import org.nonamespace.word.server.entity.DataEntity;
import org.nonamespace.word.server.enums.*;
import org.nonamespace.word.server.mapper.CourseMapper;
import org.nonamespace.word.server.mapper.StudentTextbookProgressMapper;
import org.nonamespace.word.server.misc.SentenceOrderQuizGenerator;
import org.nonamespace.word.server.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.nonamespace.word.server.enums.CourseSectionTypeEnum.LEARNING;

/**
 * 课程Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements ICourseService {

    @Autowired
    private ICourseSectionService courseSectionService;

    @Autowired
    private ICourseSectionStepService courseSectionStepService;

    @Autowired
    private TextbookService textbookService;

    @Autowired
    private IWordService wordService;

    @Autowired
    private IReviewScheduleService reviewScheduleService;
    @Autowired
    private TextbookItemService textbookItemService;
    @Autowired
    private StudentTextbookProgressMapper studentTextbookProgressMapper;

    @Autowired
    private IStudentTextbookProgressService studentTextbookProgressService;

    @Autowired
    private IStudentWordProgressService studentWordProgressService;

    @Autowired
    private IStudentPointTransactionService studentPointTransactionService;

    @Autowired
    private OssService ossService;

    @Autowired
    private IUserService userService;

    @Autowired
    private UserStudentExtService userStudentExtService;

    @Autowired
    private IStudentWordTestService studentWordTestService;

    @Autowired
    private IWxSendMessageService wxSendMessageService;

    @Autowired
    private IStudentCourseHoursService studentCourseHoursService;

    @Autowired
    private IStudentCourseConsumptionService studentCourseConsumptionService;

    @Autowired
    private org.nonamespace.word.server.util.CourseHoursConfigUtil courseHoursConfigUtil;

    /**
     * 默认内置的单词选项数据，用于兜底使用
     */
    private final static List<String> DEF_WORDS_PRACTICE_LIST = CollUtil.newArrayList("鼠标", "湖泊", "椅子", "计算机", "太阳",
            "图书馆", "蛋糕", "飞机", "教师", "秋天");

    /**
     * 查询课程
     *
     * @param id     课程主键
     * @param detail 是否需要详细信息
     * @return 课程
     */
    @Override
    public CourseInfoDto.Resp courseInfo(String id, Boolean detail) {
        log.info("查询课程信息, courseId: {}", id);

        if (!StrUtil.isNotEmpty(id)) {
            log.warn("课程ID不能为空");
            return null;
        }

        Course course = getById(id);
        if (course == null) {
            log.warn("未找到课程, courseId: {}", id);
            return null;
        }

        CourseDto courseDto = buildCourseDto(course);
        if (!Boolean.TRUE.equals(detail)) {
            courseDto.setContent(null);
        }
        CourseInfoDto.Resp resp = new CourseInfoDto.Resp();
        BeanUtil.copyProperties(courseDto, resp);

        log.debug("成功查询课程信息, courseId: {}", id);
        return resp;
    }

    private CourseDto buildCourseDto(Course course) {

        String courseId = course.getId();
        // 查询课程的所有环节
        List<CourseSection> sections = courseSectionService.lambdaQuery().eq(CourseSection::getCourseId, courseId).list();
        if (CollUtil.isEmpty(sections)) {
            log.warn("课程没有环节, courseId: {}", courseId);
            return initCourseDto(course);
        }

        CourseDto courseDto = JSONUtil.toBean(course.getContent(), CourseDto.class);

        if (courseDto.getStatus().equals("已完成") || courseDto.getStatus().equals("停课")) {
            log.info("课程已结束或取消, courseId: {}", courseId);
            return courseDto;
        }

        // 已经是完成状态，直接返回
        if (isCompleted(courseDto.getStatus())) {
            return courseDto;
        }

        courseDto.setStatus(course.getCourseStatus());
        courseDto.setActualEndTime(course.getActualEndTime());
        courseDto.getContent().setCurrentSectionIndex(course.getCurrentSectionIndex());

        List<CourseSectionStep> steps = courseSectionStepService.lambdaQuery().eq(CourseSectionStep::getCourseId, courseId).list();

        Map<String, CourseSectionStep> stepMap = steps.stream().collect(Collectors.toMap(CourseSectionStep::getId, x -> x));

        Map<String, CourseSection> sectionMap = sections.stream().collect(Collectors.toMap(CourseSection::getId, x -> x));

        for (CourseSectionDto sectionDto : courseDto.getContent().getSections()) {
            CourseSection section = sectionMap.get(sectionDto.getId());
            sectionDto.setStatus(section.getStatus());
            sectionDto.setStartTime(section.getStartTime());
            sectionDto.setEndTime(section.getEndTime());
            sectionDto.setCurrentWordIndex(section.getCurrentWordIndex());
            for (CourseSectionWordDto wordDto : sectionDto.getWords()) {
                // 获取当前单词的所有步骤
                wordDto.setCurrentStepIndex(0);
                for (CourseSectionStepDto stepDto : wordDto.getSteps()) {
                    CourseSectionStep step = stepMap.get(stepDto.getId());
                    stepDto.setResult(step.getResult());
                    stepDto.setStatus(step.getStatus());
                    stepDto.setStartTime(step.getStartTime());
                    stepDto.setEndTime(step.getEndTime());
                    stepDto.setStudentAnswer(step.getStudentAnswer());
                    if("已完成".equals(step.getStatus())) {
                        wordDto.setCurrentStepIndex(wordDto.getCurrentStepIndex() + 1);
                    }
                }
                if(wordDto.getCurrentStepIndex() >= wordDto.getSteps().size()) {
                    wordDto.setCurrentStepIndex(wordDto.getSteps().size() -1);
                }
            }
        }

        return courseDto;
    }

    /**
     * 判断课程状态是否是完成态
     *
     * @param status 状态
     * @return CourseDto
     */
    private boolean isCompleted(String status) {
        return "已完成".equals(status) || "停课".equals(status);
    }


    /**
     * 构建课程环节单词步骤数据
     *
     * @param textbookItemIds
     * @return
     */
    private List<CourseSectionWordDto> buildSectionWords(CourseSection section, List<String> textbookItemIds, List<WordLearnStepTypeEnum> stepTypes) throws IOException {

        if (CollUtil.isEmpty(textbookItemIds)) {
            throw new IllegalArgumentException("教材单元ID为空");
        }
        // 去重
        textbookItemIds = textbookItemIds.stream().distinct().toList();

        Map<String, TextbookItem> textbookItemMap = textbookItemService.lambdaQuery()
                .select(TextbookItem::getId, TextbookItem::getTextbookId, TextbookItem::getWordId, TextbookItem::getVideoUrl).in(TextbookItem::getId, textbookItemIds)
                .orderByAsc(TextbookItem::getDisplayOrder).list().stream()
                .collect(Collectors.toMap(TextbookItem::getId, Function.identity(), (v1, v2) -> v1, LinkedHashMap::new));

        if (textbookItemMap.isEmpty()) {
            throw new IllegalArgumentException("部分教材单元ID不存在或已被删除");
        }

        Map<String, Textbook> textbookMap = textbookService.lambdaQuery().select(Textbook::getId, Textbook::getType, Textbook::getGrade, Textbook::getStage).in(Textbook::getId, textbookItemMap.values().stream().map(TextbookItem::getTextbookId).collect(Collectors.toSet())).list().stream().collect(Collectors.toMap(Textbook::getId, x -> x));
        Map<String, Word> wordMap = wordService.lambdaQuery().in(Word::getId, textbookItemMap.values().stream().map(TextbookItem::getWordId).collect(Collectors.toSet())).list().stream().collect(Collectors.toMap(Word::getId, x -> x));
        WssContext.set("_sectionWordMap", wordMap);
        // 从数据库中随机获取20个单词
        List<Word> randomWords = wordService.selectRandomList(20);

        List<CourseSectionWordDto> wordDtos = new ArrayList<>();
        List<CourseSectionStep> allWordSteps = new ArrayList<>();

        List<CourseSectionWordInfo> wordInfos = new ArrayList<>();

        for (TextbookItem tbi : textbookItemMap.values()) {
            String wordId = tbi.getWordId();
            List<CourseSectionStep> wordSteps = new ArrayList<>();

            String textbookType = TextBookTypeEnum.SPECIAL.getValue().equals(textbookMap.get(tbi.getTextbookId()).getType()) ? "特色" : "通用";
            String stage = textbookMap.get(tbi.getTextbookId()).getStage();

            CourseSectionWordDto wordDto = new CourseSectionWordDto();
            wordDtos.add(wordDto);
            wordDto.setId(wordId);

            Word word = wordMap.get(wordId);

            CourseSectionWordInfo wordInfo = new CourseSectionWordInfo();
            wordInfos.add(wordInfo);
            convertWordToDto(word, wordInfo);
            if (StrUtil.isNotEmpty(tbi.getVideoUrl())) {
                wordInfo.setVideoUrl(tbi.getVideoUrl());
            } else {
                wordInfo.setVideoUrl(word.getVideoUrl() != null ? word.getVideoUrl().getOrDefault(textbookType, word.getVideoUrl().get("通用")) : null);
            }
            wordInfo.setMeanings(getMeaningsByTextbookType(word, textbookType));
//            Optional<List<Word.Sentences>> sentencesOpt = Optional.ofNullable(word.getSentences())
//                    .map(m -> m.getOrDefault(textbookType, m.get("通用")));
//            if (sentencesOpt.isEmpty()) {
//                throw new IOException(String.format("单词[%s]的例句不全，请重新选择其他单词", word.getWord()));
//            }
//            wordInfo.setSentences(getSentencesByStage(sentencesOpt.get(), stage));
            wordInfo.setSentences(getSentencesByStage(word, stage, textbookType));

            if (word.getSentences() == null) {
                throw new IOException(String.format("单词[%s]信息补全，请重新选择其他单词", word.getWord()));
            }

            wordDto.setWordInfo(wordInfo);

            courseSectionStepService.buildWordSteps(section.getCourseId(), section.getId(), tbi.getId(), stepTypes).forEach(step -> {
                step.setTextbookId(tbi.getTextbookId());
                step.setTextbookItemId(tbi.getId());
                step.setWordId(wordId);
                step.setWord(word.getWord());
                wordSteps.add(step);
            });


            List<CourseSectionStepDto> stepList = wordSteps.stream()
                    .sorted(Comparator.comparing(CourseSectionStep::getOrderIndex))
                    .map(x -> {
                        try {
                            return buildStepDto(x, wordDto.getWordInfo(), randomWords);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
            wordDto.setSteps(stepList);


            wordInfo.getMeanings().setPractices(null);
            wordInfo.getSentences().setStructurePartsEn(null);
            wordInfo.getSentences().setPractices(null);

            allWordSteps.addAll(wordSteps);
        }

        section.getWords().set("wordInfos", wordInfos);

        courseSectionStepService.saveBatch(allWordSteps);

        return wordDtos;

    }


    /**
     * 构建课程环节单词步骤数据
     *
     * @param textbookItemIds
     * @return
     */
    @Override
    public List<CourseSectionWordInfo> buildWords(List<String> textbookItemIds, boolean showDisplayOrder) {

        if (CollUtil.isEmpty(textbookItemIds)) {
            throw new IllegalArgumentException("教材单元ID为空");
        }
        // 去重
        textbookItemIds = textbookItemIds.stream().distinct().toList();
        Map<String, TextbookItem> textbookItemMap = textbookItemService.lambdaQuery()
                .select(TextbookItem::getId, TextbookItem::getTextbookId, TextbookItem::getWordId, TextbookItem::getVideoUrl, TextbookItem::getDisplayOrder).in(TextbookItem::getId, textbookItemIds)
                .orderByAsc(TextbookItem::getDisplayOrder).list().stream()
                .collect(Collectors.toMap(TextbookItem::getId, Function.identity(), (v1, v2) -> v1));

        if (textbookItemIds.size() != textbookItemMap.size()) {
            throw new IllegalArgumentException("部分教材单元ID不存在或已被删除");
        }

        List<TextbookItem> textbookItems = textbookItemMap.values().stream().sorted(Comparator.comparing(TextbookItem::getDisplayOrder)).toList();
        Map<String, Textbook> textbookMap = textbookService.lambdaQuery().select(Textbook::getId, Textbook::getType, Textbook::getGrade, Textbook::getStage).in(Textbook::getId, textbookItems.stream().map(TextbookItem::getTextbookId).collect(Collectors.toSet())).list().stream().collect(Collectors.toMap(Textbook::getId, x -> x));
        Map<String, Word> wordMap = wordService.lambdaQuery().in(Word::getId, textbookItems.stream().map(TextbookItem::getWordId).collect(Collectors.toSet())).list().stream().collect(Collectors.toMap(Word::getId, x -> x));

        List<CourseSectionWordInfo> wordInfos = new ArrayList<>();

        for (TextbookItem tbi : textbookItems) {
            String wordId = tbi.getWordId();

            String textbookType = TextBookTypeEnum.SPECIAL.getValue().equals(textbookMap.get(tbi.getTextbookId()).getType()) ? "特色" : "通用";
            String stage = textbookMap.get(tbi.getTextbookId()).getStage();

            CourseSectionWordDto wordDto = new CourseSectionWordDto();
            wordDto.setId(wordId);
            Word word = wordMap.get(wordId);

            CourseSectionWordInfo wordInfo = new CourseSectionWordInfo();

            if(showDisplayOrder){
                wordInfo.setDisplayOrder(tbi.getDisplayOrder());
            }else{
                wordInfo.setDisplayOrder(null);
            }

            wordInfos.add(wordInfo);
            convertWordToDto(word, wordInfo);
            if (StrUtil.isNotEmpty(tbi.getVideoUrl())) {
                wordInfo.setVideoUrl(tbi.getVideoUrl());
            } else {
                wordInfo.setVideoUrl(word.getVideoUrl() != null ? word.getVideoUrl().getOrDefault(textbookType, word.getVideoUrl().get("通用")) : null);
            }
//            wordInfo.setMeanings(
//                    Optional.ofNullable(word.getMeanings())
//                            .map(m -> m.getOrDefault(textbookType, m.get("通用")))
//                            .orElse(null)
//            );
//            Optional<List<Word.Sentences>> sentencesOpt = Optional.ofNullable(word.getSentences())
//                    .map(m -> m.getOrDefault(textbookType, m.get("通用")));
//            wordInfo.setSentences(getSentencesByStage(sentencesOpt.orElse(null), stage));

            wordInfo.setMeanings(getMeaningsByTextbookType(word, textbookType));
            wordInfo.setSentences(getSentencesByStage(word, stage, textbookType));

            wordDto.setWordInfo(wordInfo);


            if (wordInfo.getMeanings() != null) {
                wordInfo.getMeanings().setPractices(null);
            }
            if (wordInfo.getSentences() != null) {
                wordInfo.getSentences().setStructurePartsEn(null);
                wordInfo.getSentences().setPractices(null);
            }

        }

        return wordInfos;

    }


    /**
     * 将Word对象转换为CourseSectionWordInfo DTO
     *
     * @param word     word实体
     * @param wordInfo wordInfo DTO
     */
    private void convertWordToDto(Word word, CourseSectionWordInfo wordInfo) {
        wordInfo.setId(word.getId());
        wordInfo.setWord(word.getWord());
        wordInfo.setSyllables(word.getSyllables());
        wordInfo.setPhoneticUk(word.getPhoneticUk());
        wordInfo.setPhoneticUs(word.getPhoneticUs());
        wordInfo.setDifficulty(word.getDifficulty().toString());
        wordInfo.setAudioUkUrl(word.getAudioUkUrl());
        wordInfo.setAudioUsUrl(word.getAudioUsUrl());
    }

    /**
     * 获取中文释义
     *
     * @param word
     * @param textbookType
     * @return
     */
    private Word.Meanings getMeaningsByTextbookType(Word word, String textbookType) {
        if (word.getMeanings() != null) {
            Word.Meanings meanings = word.getMeanings().get(textbookType);
            if (meanings == null || CollUtil.isEmpty(meanings.getPractices())) {
                meanings = word.getMeanings().values().stream().filter(x -> CollUtil.isNotEmpty(x.getPractices())).findAny().orElse(null);
            }
            return meanings;
        }
        return null;
    }

    private Word.Sentences getSentencesByStage(Word word, String stage, String textbookType) {
        if (word.getSentences() == null) {
            return null;
        }
        List<Word.Sentences> sentences = word.getSentences().get(textbookType);
        if (CollUtil.isEmpty(sentences)) {
            sentences = word.getSentences().get(StrUtil.equals(textbookType, "通用") ? "特色" : "通用");
        }
        if (CollUtil.isEmpty(sentences)) {
            return null;
        }

        List<Word.Sentences> finalSentences = sentences;
        return sentences.stream().filter(s ->
                        StrUtil.isNotEmpty(s.getSentenceEn()) && CollUtil.isNotEmpty(s.getPractices())
                                && CollUtil.isNotEmpty(s.getStructurePartsEn()) && StrUtil.equals(stage, s.getStage()))
                .findFirst().orElseGet(() -> finalSentences.stream().filter(s ->
                        StrUtil.isNotEmpty(s.getSentenceEn()) && CollUtil.isNotEmpty(s.getPractices())
                                && CollUtil.isNotEmpty(s.getStructurePartsEn())).findFirst().orElse(null));
    }


    /**
     * 获取指定阶段的句子
     *
     * @param sentences
     * @param stage
     * @return
     */
    private Word.Sentences getSentencesByStage(List<Word.Sentences> sentences, String stage) {
        if (CollUtil.isEmpty(sentences)) return null;
        sentences.forEach(s -> {
            if (StrUtil.isEmpty(s.getStage())) s.setStage("通用");
        });
        Map<String, List<Word.Sentences>> stageMap = sentences.stream().collect(Collectors.groupingBy(Word.Sentences::getStage));
        List<String> stages = Arrays.asList("小学", "初中", "高中");
        int idx = Math.max(stages.indexOf(stage), 0);
        return stages.stream()
                .map(s -> stageMap.getOrDefault(stages.get((idx + stages.indexOf(s)) % stages.size()), Collections.emptyList()))
                .filter(list -> !list.isEmpty())
                .map(List::getFirst)
                .findFirst()
                .orElse(stageMap.getOrDefault("通用", Collections.emptyList()).stream().findFirst().orElse(null));
    }

    private CourseSectionStepDto buildStepDto(CourseSectionStep step, CourseSectionWordInfo wordInfo, List<Word> randomWords) throws IOException {
        CourseSectionStepDto stepDto = new CourseSectionStepDto();
        BeanUtil.copyProperties(step, stepDto);

        if (StrUtil.isEmpty(stepDto.getAnswer())) {
            buildPractices(stepDto, wordInfo, randomWords);

            step.setOptionsData(stepDto.getOptions());
            step.setOrdering(stepDto.getSentenceOrder());
            step.setAnswer(stepDto.getAnswer());
        }


        return stepDto;
    }

    /**
     * 构建测试题目
     *
     * @param step
     * @param wordInfo
     */
    private void buildPractices(CourseSectionStepDto step, CourseSectionWordInfo wordInfo, List<Word> randomWords) throws IOException {
        Map<String, Word> wordMap = WssContext.get("_sectionWordMap");
        WordLearnStepTypeEnum type = WordLearnStepTypeEnum.typeOf(step.getType());
        if (type == WordLearnStepTypeEnum.WORD_PRACTICE) {
            // 随机取wordInfo.getMeanings().getPractices()3个元素并打乱顺序
            List<String> practices = wordInfo.getMeanings().getPractices();
            if (CollUtil.isEmpty(practices)) {
                practices = DEF_WORDS_PRACTICE_LIST;
            }
            Collections.shuffle(practices);
            practices = new ArrayList<>(practices.subList(0, 3));

            // 这里使用除自己以外的，其他单词的干扰项
            if (CollUtil.isNotEmpty(randomWords)) {
                // 这里实现除了自己以外，随机获取其他单词的getMeanings().getPractices() 三个元素并打乱顺序
                List<Word> otherWords = new ArrayList<>(randomWords.stream().filter(w -> !w.getId().equalsIgnoreCase(wordInfo.getId())).toList());
                if (CollUtil.isNotEmpty(otherWords)) {
                    List<String> defs = new ArrayList<>();
                    List<String> finalDefs = defs;
                    otherWords.stream()
                            .filter(otherWord -> CollUtil.isNotEmpty(otherWord.getMeanings()))
                            .forEach(w -> {
                                w.getMeanings().values()
                                        .stream().filter(v -> CollUtil.isNotEmpty(v.getPos()))
                                        .forEach(v -> finalDefs.addAll(v.getPos().stream().map(Word.Meanings.Pos::getDef).toList()));
                            });
                    defs = finalDefs.stream().distinct().collect(Collectors.toList());
                    Collections.shuffle(defs);
                    // 有几个替换几个，有可能只选了一个单词。根本不具备生成4个选项。
                    List<String> replacePractices = defs.subList(0, Math.min(defs.size(), 3));
                    for (int i = 0; i < (Math.min(replacePractices.size(), 3)); i++) {
                        practices.set(i, replacePractices.get(i));
                    }
                }
                Collections.shuffle(practices);
            }

            // 生成0-3随机整数
            int answerIndex = new Random().nextInt(4);
            // 正确答案是单词的中文含义
            List<Word.Meanings.Pos> posList = wordInfo.getMeanings().getPos();
            if (CollUtil.isEmpty(posList) && posList.stream().allMatch(pos -> pos.getDef().isEmpty())) {
                throw new IOException(String.format("单词[%s]的中文释义信息不全，请选择其他单词", wordInfo.getWord()));
            }
            String correctAnswer = posList.stream().map(Word.Meanings.Pos::getDef).collect(Collectors.joining("；"));
            practices.add(answerIndex, correctAnswer);

            step.setOptions(practices);
            step.setAnswer(String.valueOf(answerIndex));
        }
        // 单词测验，选项为英语: 中文释义
        if (type == WordLearnStepTypeEnum.WORD_PRACTICE_2) {
            // 随机取wordInfo.getMeanings().getPractices()3个元素并打乱顺序
            List<String> practices = wordInfo.getMeanings().getPractices().stream().map(o -> wordInfo.getWord() + StrPool.COLON + " " + o).collect(Collectors.toList());
            if (CollUtil.isEmpty(practices)) {
                practices = DEF_WORDS_PRACTICE_LIST;
            }
            Collections.shuffle(practices);
            practices = new ArrayList<>(practices.subList(0, 3));

            // 这里使用除自己以外的，其他单词的干扰项
            if (CollUtil.isNotEmpty(randomWords)) {
                // 这里实现除了自己以外，随机获取其他单词的getMeanings().getPractices() 三个元素并打乱顺序
                List<Word> otherWords = new ArrayList<>(randomWords.stream().filter(w -> !w.getId().equalsIgnoreCase(wordInfo.getId())).toList());
                if (CollUtil.isNotEmpty(otherWords)) {
                    List<String> defs = new ArrayList<>();
                    List<String> finalDefs = defs;
                    otherWords.stream()
                            .filter(otherWord -> CollUtil.isNotEmpty(otherWord.getMeanings()))
                            .forEach(w -> {
                                w.getMeanings().values()
                                        .stream().filter(v -> CollUtil.isNotEmpty(v.getPos()))
                                        .forEach(v -> finalDefs.addAll(v.getPos().stream().map(pos -> w.getWord() + StrPool.COLON + " " + pos.getDef()).toList()));
                            });
                    defs = finalDefs.stream().distinct().collect(Collectors.toList());
                    Collections.shuffle(defs);
                    // 有几个替换几个，有可能只选了一个单词。根本不具备生成4个选项。
                    List<String> replacePractices = defs.subList(0, Math.min(defs.size(), 3));
                    for (int i = 0; i < (Math.min(replacePractices.size(), 3)); i++) {
                        practices.set(i, replacePractices.get(i));
                    }
                }
                Collections.shuffle(practices);
            }

            // 生成0-3随机整数
            int answerIndex = new Random().nextInt(4);
            // 正确答案是单词的中文含义
            List<Word.Meanings.Pos> posList = wordInfo.getMeanings().getPos();
            if (CollUtil.isEmpty(posList) && posList.stream().allMatch(pos -> pos.getDef().isEmpty())) {
                throw new IOException(String.format("单词[%s]的中文释义信息不全，请选择其他单词", wordInfo.getWord()));
            }
            String correctAnswer = wordInfo.getWord() + StrPool.COLON + " " + wordInfo.getMeanings().getPos().stream().map(Word.Meanings.Pos::getDef).collect(Collectors.joining("；"));
            practices.add(answerIndex, correctAnswer);

            step.setOptions(practices);
            step.setAnswer(String.valueOf(answerIndex));
        }

        if (type == WordLearnStepTypeEnum.SENTENCE_TRANSLATE) {

            List<String> sentences = wordInfo.getSentences().getPractices();

            // 如果sentences为空，则从其他单词中随机取两个
            if (CollUtil.isEmpty(sentences)) {
                Word w = wordMap.get(wordInfo.getId());
                Set<String> cnSet = w.getSentences().values().stream().flatMap(Collection::stream).map(x -> CollUtil.isNotEmpty(x.getPractices()) ? x.getPractices() : CollUtil.list(false, x.getSentenceCn())).flatMap(Collection::stream).filter(s -> Objects.equals(s, wordInfo.getSentences().getSentenceCn())).collect(Collectors.toSet());
                if (cnSet.size() < 2) {
                    Word randomWord = randomWords.get(RandomUtil.randomInt(randomWords.size()));
                    if (!randomWord.getId().equals(wordInfo.getId())) {
                        cnSet.addAll(randomWord.getSentences().values().stream().flatMap(Collection::stream).map(x -> CollUtil.isNotEmpty(x.getPractices()) ? x.getPractices() : CollUtil.list(false, x.getSentenceCn())).flatMap(Collection::stream).toList());
                    }
                    sentences = new ArrayList<>(cnSet);
                    sentences = sentences.stream().distinct().collect(Collectors.toList());
                } else {
                    sentences = new ArrayList<>(cnSet);
                    sentences = sentences.stream().distinct().collect(Collectors.toList());
                }
            }


            Collections.shuffle(sentences);
            sentences = new ArrayList<>(sentences.subList(0, 2));

            // 生成0-3随机整数
            int answerIndex = new Random().nextInt(3);

            sentences.add(answerIndex, wordInfo.getSentences().getSentenceCn());

            step.setOptions(sentences);
            step.setAnswer(String.valueOf(answerIndex));
        }

        if (type == WordLearnStepTypeEnum.SENTENCE_ORDER) {

            String structurePartsEn = wordInfo.getSentences().getStructurePartsEn().get(RandomUtil.randomInt(wordInfo.getSentences().getStructurePartsEn().size()));

            SentenceOrderQuizGenerator.SentenceQuiz quiz = SentenceOrderQuizGenerator.generateQuiz(structurePartsEn);
            step.setOptions(quiz.getOptionsText());
            step.setSentenceOrder(quiz.getQuestionSegments());
            step.setAnswer(CollUtil.join(quiz.getCorrectSequenceIndices(), ", "));
        }

        // 单词填空题：给出单词的部分字母，学生填写完整单词
        if (type == WordLearnStepTypeEnum.WORD_FILL_2 || type == WordLearnStepTypeEnum.WORD_FILL_3 || type == WordLearnStepTypeEnum.WORD_LISTEN) {
            step.setType(WordLearnStepTypeEnum.WORD_LISTEN.getValue());
            String word = wordInfo.getWord();
            List<Integer> markIndexs = maskWord(word, type == WordLearnStepTypeEnum.WORD_LISTEN ? word.length() : (type == WordLearnStepTypeEnum.WORD_FILL_2 ? 2 : 3));
            step.setOptions(markIndexs.stream().map(String::valueOf).collect(Collectors.toList()));
            step.setAnswer(StrUtil.join("", markIndexs.stream().map(word::charAt).toList()));
        }

        // 句子填空题：句子中挖空关键词
        if (type == WordLearnStepTypeEnum.SENTENCE_FILL_3 || type == WordLearnStepTypeEnum.SENTENCE_FILL_4) {

            step.setType(WordLearnStepTypeEnum.SENTENCE_FILL.getValue());
            int maskCount = type == WordLearnStepTypeEnum.SENTENCE_FILL_3 ? 3 : 4;
            String sentence = wordInfo.getSentences().getSentenceEn();
            maskSentence(sentence, maskCount, step);
        }
    }

    /**
     * 给定一个英文句子，和要挖空的数量，随机挖掉句子中的指定数量的单词，并打乱单词顺序，给出正确的顺序下标
     *
     * @param sentence
     * @param maskCount
     * @param step
     */
    private void maskSentence(String sentence, int maskCount, CourseSectionStepDto step) {
        List<Integer> wordIndices = new ArrayList<>();
        // 按非字母分割句子，得到单词数组
        String[] words = sentence.split("\\s+");
        // 只挖非标点的单词
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            // 判断是否为纯标点
            if (word.chars().anyMatch(Character::isLetter)) {
                wordIndices.add(i);
            }
        }

        Collections.shuffle(wordIndices);

        if (wordIndices.size() <= maskCount) {
            maskCount = wordIndices.size();
        }
        List<Integer> indexs = wordIndices.stream().limit(maskCount).collect(Collectors.toList());

        step.setOptions(indexs.stream().map(String::valueOf).collect(Collectors.toList()));
        step.setAnswer(StrUtil.join(", ", indexs));
        step.setSentenceOrder(indexs.stream().map(i -> words[i]).collect(Collectors.toList()));
    }

    /**
     * 遮盖单词的部分字母
     *
     * @param word      原始单词
     * @param maskCount 遮盖的字母数量
     * @return 遮盖后的单词
     */
    private List<Integer> maskWord(String word, int maskCount) {
        List<Integer> letterIndices = new ArrayList<>();
        char[] chars = word.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (Character.isLetter(chars[i])) {
                letterIndices.add(i);
            }
        }
        if (letterIndices.size() <= maskCount) {
            return IntStream.range(0, letterIndices.size()).boxed().toList();
        }
        Collections.shuffle(letterIndices);
        return letterIndices.stream().limit(maskCount).collect(Collectors.toList());
    }

    @Override
    public void deleteNotStartedCourses(String teacherId, String studentId, String subject, String specification, String remark) {
        lambdaUpdate()
                .set(Course::getCourseStatus, "停课")
                .set(Course::getDeleted, true)
//                .set(Course::getRemark, remark)
                .set(Course::getCancelReason, remark)
                .set(Course::getUpdateTime, WssContext.now())
                .set(Course::getUpdateBy, WssContext.userId())
                .eq(StrUtil.isNotEmpty(teacherId), Course::getTeacherId, teacherId)
                .eq(StrUtil.isNotEmpty(studentId), Course::getStudentId, studentId)
                .eq(StrUtil.isNotEmpty(subject), Course::getSubject, subject)
                .eq(StrUtil.isNotEmpty(specification), Course::getSpecification, specification)
                .eq(Course::getCourseStatus, "待开始")
                .update();

        log.info("删除未开始课程，参数: teacherId={}, studentId={}, subject={}, specification={}", teacherId, studentId, subject, specification);
    }

    @Override
    public void startCourse(String id) {
        log.info("开始上课, courseId: {}", id);

        if (!StrUtil.isNotEmpty(id)) {
            throw new IllegalArgumentException("课程ID不能为空");
        }

        Course course = getById(id);
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        checkCourseTeacher(course);

        if (course.getCourseStatus().equals("停课")) {
            throw new IllegalArgumentException("当前课程已停课");
        }

        if (!"待开始".equals(course.getCourseStatus())) {
            log.warn("【开始上课】 课程状态不正确，当前状态: {}", course.getCourseStatus());
            return;
        }

        boolean sameDay = DateUtil.isSameDay(course.getScheduledStartTime(), WssContext.now());
        if (!sameDay) {
            log.warn("【开始上课】 只能开始今天的课程");
            throw new IllegalArgumentException("只能开始今天的课程");
        }

        // 检查课时余额是否足够（根据Redis课消开关控制）
        if (courseHoursConfigUtil.isCourseHoursEnabled()) {
            checkCourseHoursBalance(course);
        } else {
            log.info("课消功能已关闭，跳过课时余额检查: courseId={}", id);
        }

        course.setCurrentSectionIndex(-1L);
        course.setCourseStatus("进行中");
        course.setActualStartTime(WssContext.now());
        course.setUpdateTime(WssContext.now());

        initCourseDto(course);


        boolean result = updateById(course);
        if (!result) {
            throw new RuntimeException("开始上课失败");
        }

        log.info("成功开始上课, courseId: {}", id);
    }

    @Override
    public void preflightCourse(String id) {
        log.info("上课预检, courseId: {}", id);

        if (!StrUtil.isNotEmpty(id)) {
            throw new IllegalArgumentException("课程ID不能为空");
        }

        Course course = getById(id);
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        checkCourseTeacher(course);

        if (course.getCourseStatus().equals("停课")) {
            throw new IllegalArgumentException("当前课程已停课");
        }

        if (!"待开始".equals(course.getCourseStatus())) {
            log.warn("【上课预检】 课程状态不正确，当前状态: {}", course.getCourseStatus());
            return;
        }

        boolean sameDay = DateUtil.isSameDay(course.getScheduledStartTime(), WssContext.now());
        if (!sameDay) {
            log.warn("【上课预检】 只能开始今天的课程");
            throw new IllegalArgumentException("只能开始今天的课程");
        }

        // 检查课时余额是否足够（根据Redis课消开关控制）
        if (courseHoursConfigUtil.isCourseHoursEnabled()) {
            checkCourseHoursBalance(course);
        } else {
            log.info("课消功能已关闭，跳过课时余额检查: courseId={}", id);
        }
    }

    /**
     * 初始化课程DTO
     *
     * @param course 课程实体
     */
    private static CourseDto initCourseDto(Course course) {
        if (course.getContent() != null && !course.getContent().isEmpty()) {
            CourseDto courseDto = JSONUtil.toBean(course.getContent(), CourseDto.class);
            courseDto.setStatus(course.getCourseStatus());
        }
        CourseDto courseDto = new CourseDto();
        // 设置教师信息 (mock数据，实际应该从用户表查询)
        CourseDto.Teacher teacher = new CourseDto.Teacher();
        teacher.setId(WssContext.userId());
        teacher.setName("教师姓名");
        teacher.setAvatar("头像URL");
        courseDto.setTeacher(teacher);

        // 设置学生信息 (mock数据，实际应该从用户表查询)
        CourseDto.Student student = new CourseDto.Student();
        student.setId(course.getStudentId());
        student.setName("学生姓名");
        student.setAvatar("头像URL");
        courseDto.setStudent(student);

        courseDto.setId(course.getId());
        courseDto.setType(course.getType());
        courseDto.setStatus(course.getCourseStatus());
        courseDto.setScheduledStartTime(course.getScheduledStartTime());
        courseDto.setScheduledEndTime(course.getScheduledEndTime());
        courseDto.setActualStartTime(course.getActualStartTime());
        courseDto.setActualEndTime(course.getActualEndTime());
        courseDto.setDurationMinutes(course.getDurationMinutes());
        courseDto.setContent(new CourseDto.Content());
        courseDto.getContent().setCurrentSectionIndex(-1L);
        courseDto.getContent().setSections(new ArrayList<>());
        course.setContent(JSONUtil.parseObj(courseDto));

        return courseDto;
    }

    private void checkCourseStatus(Course course) {
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        if (!StrUtil.equals("单词课", course.getSpecification())) {
            throw new IllegalArgumentException("不是单词课，不能进行此操作");
        }

        if (!"进行中".equals(course.getCourseStatus())) {
            throw new IllegalStateException("课程状态不正确，当前状态: " + course.getCourseStatus());
        }

    }

    /**
     * 检查课程是否为学习课
     *
     * @param course
     */
    private void checkCourseType(Course course) {
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }
        if (!"学习课".equals(course.getType())) {
            throw new IllegalStateException("不是学习课，不能做此操作");
        }
    }

    /**
     * 检查当前用户是否为课程的教师
     *
     * @param course 课程实体
     */
    private void checkCourseTeacher(Course course) {
        if (!Objects.equals(course.getTeacherId(), WssContext.userId())) {
            throw new IllegalArgumentException("你不是这节课的老师，不能进行此操作");
        }
    }

    /**
     * 检查课时余额是否足够课消
     * 只有学习课才需要检查课时余额
     *
     * @param course 课程实体
     */
    private void checkCourseHoursBalance(Course course) {
        // 只有学习课才需要检查课时余额
        if (!"学习课".equals(course.getType())) {
            return;
        }

        try {
            log.info("检查课时余额: courseId={}, studentId={}, subject={}, specification={}, duration={}",
                    course.getId(), course.getStudentId(), course.getSubject(),
                    course.getSpecification(), course.getDurationMinutes());

            // 验证课程信息
            if (course.getStudentId() == null) {
                log.warn("课程学生ID为空，跳过课时检查: courseId={}", course.getId());
                return;
            }

            if (StrUtil.isBlank(course.getSubject()) || StrUtil.isBlank(course.getSpecification())) {
                log.warn("课程学科或课型为空，跳过课时检查: courseId={}, subject={}, specification={}",
                        course.getId(), course.getSubject(), course.getSpecification());
                return;
            }

            // 计算需要消费的课时数：按排课时长计算，1课时=60分钟
            Long durationMinutes = course.getDurationMinutes();
            if (durationMinutes == null || durationMinutes <= 0) {
                log.warn("课程时长无效，跳过课时检查: courseId={}, duration={}", course.getId(), durationMinutes);
                return;
            }

            // 计算需要消费的课时数，保留2位小数
            BigDecimal requiredHours = BigDecimal.valueOf(durationMinutes)
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);

            log.info("计算需要课时数: courseId={}, duration={}分钟, requiredHours={}课时",
                    course.getId(), durationMinutes, requiredHours);

            // 使用学生课时服务检查余额（包含课程性质匹配）
            IStudentCourseHoursService.CourseHoursCheckResult checkResult =
                    studentCourseHoursService.checkHoursBalance(course.getStudentId(), course.getSubject(), course.getSpecification(), course.getCourseType(), requiredHours);

            if (!checkResult.isSufficient()) {
                log.warn("课时余额检查失败: courseId={}, studentId={}, message={}", course.getId(), course.getStudentId(), checkResult.getMessage());
                throw new IllegalStateException(checkResult.getMessage());
            }

            log.info("课时余额检查通过: courseId={}, studentId={}, remaining={}, required={}",
                    course.getId(), course.getStudentId(), checkResult.getRemainingHours(), checkResult.getRequiredHours());

        } catch (Exception e) {
            if (e instanceof IllegalStateException) {
                throw e; // 重新抛出业务异常
            }
            log.error("课时余额检查失败: courseId={}, error={}", course.getId(), e.getMessage(), e);
            throw new RuntimeException("课时余额检查失败: " + e.getMessage(), e);
        }
    }


    private void updateStudentLearnProgress(String courseId, String textbookItemId, boolean isCorrect) {
        log.info("更新学生学习进度, courseId: {}, textbookItemId: {}, isCorrect: {}", courseId, textbookItemId, isCorrect);

        Course course = getById(courseId);
        TextbookItem textbookItem = textbookItemService.getById(textbookItemId);
        Optional<StudentTextbookProgress> stp = studentTextbookProgressService.lambdaQuery()
                .eq(StudentTextbookProgress::getStudentId, course.getStudentId())
                .eq(StudentTextbookProgress::getTextbookId, textbookItem.getTextbookId()).oneOpt();
        StudentTextbookProgress sp;
        if (stp.isEmpty()) {
            // 如果没有找到学生的教材进度记录，创建一个新的
            sp = new StudentTextbookProgress();
            sp.setId(IdUtil.getSnowflakeNextIdStr());
            sp.setStudentId(course.getStudentId());
            sp.setTextbookId(textbookItem.getTextbookId());
            sp.setStatus("学习中");
            sp.setFirstLearnAt(WssContext.now());
            sp.setLastLearnAt(WssContext.now());
            sp.setStatWordCnt(textbookItemService.lambdaQuery().eq(TextbookItem::getTextbookId, textbookItem.getTextbookId()).eq(TextbookItem::getNodeType, TextBookNodeEnum.WORD.getValue()).count());
            sp.setStatWordMistakesCnt(isCorrect ? 0L : 1L);
            studentTextbookProgressService.save(sp);
        } else {
            // 更新已有的学生教材进度记录
            sp = stp.get();
            sp.setLastLearnAt(WssContext.now());
            sp.setStatWordMistakesCnt(sp.getStatWordMistakesCnt() + (isCorrect ? 0L : 1L));
            studentTextbookProgressService.updateById(stp.get());
        }


        Optional<StudentWordProgress> swp = studentWordProgressService.lambdaQuery()
                .eq(StudentWordProgress::getStudentId, course.getStudentId())
                .eq(StudentWordProgress::getTextbookId, textbookItem.getTextbookId())
                .eq(StudentWordProgress::getTextbookItemId, textbookItemId)
                .oneOpt();
        StudentWordProgress sw;
        if (swp.isEmpty()) {
            sw = new StudentWordProgress();
            sw.setId(IdUtil.getSnowflakeNextIdStr());
            sw.setStudentId(course.getStudentId());
            sw.setTextbookId(textbookItem.getTextbookId());
            sw.setTextbookItemId(textbookItemId);
            sw.setStatus("已学习");
            sw.setLearnedInCourseId(courseId);
            sw.setLastStudiedAt(WssContext.now());
            sw.setMistakes(!isCorrect);
            sw.setWordId(textbookItem.getWordId());
            studentWordProgressService.save(sw);
        } else {
            sw = swp.get();
            sw.setLastStudiedAt(WssContext.now());
            sw.setMistakes(!isCorrect);
            sw.setLearnedInCourseId(courseId);
            if (!sw.getStatus().equals("已学习")) {
                sw.setStatus("已学习");
            }
            studentWordProgressService.updateById(sw);
        }

        log.info("学生学习进度更新成功, courseId: {}, textbookItemId: {}, isCorrect: {}", courseId, textbookItemId, isCorrect);
    }

    @Override
    public CourseStartLearningDto.Resp startLearning(String id, CourseStartLearningDto.Req req) throws IOException {
        log.info("开始新课程学习, courseId: {}, textbookId: {}, unitId: {}, wordIds: {}, textbookItemIds: {}",
                id, req.getTextbookId(), req.getUnitId(), req.getWordIds(), req.getTextbookItemIds());

        if (!StrUtil.isNotEmpty(id)) {
            throw new IllegalArgumentException("课程ID不能为空");
        }
        if (CollectionUtils.isEmpty(req.getTextbookItemIds())) {
            throw new IllegalArgumentException("词表项列表不能为空");
        }

        Course course = getById(id);

        checkCourseStatus(course);

        checkCourseTeacher(course);

        checkCourseType(course);


        CourseSectionDto courseSectionDto = createCourseSection(course, "新单词学习", CourseSectionTypeEnum.LEARNING.getValue(), null, req.getTextbookItemIds(), WordLearnStepTypeEnum.forLearn());

        CourseStartLearningDto.Resp resp = new CourseStartLearningDto.Resp();

        BeanUtil.copyProperties(courseSectionDto, resp);

        log.info("成功开始新课程学习, courseId: {}, sectionId: {}", id, courseSectionDto.getId());
        return resp;
    }


    @Override
    public CourseStartReviewDto.Resp startReview(String id, String reviewId) throws IOException {
        log.info("开始抗遗忘复习, courseId: {}, reviewId: {}", id, reviewId);

        if (!StrUtil.isNotEmpty(id)) throw new IllegalArgumentException("课程ID不能为空");
        if (!StrUtil.isNotEmpty(reviewId)) throw new IllegalArgumentException("复习计划ID不能为空");

        Course course = getById(id);
        checkCourseStatus(course);
        checkCourseTeacher(course);

        ReviewSchedule reviewSchedule = reviewScheduleService.getById(reviewId);
        if (reviewSchedule == null) throw new IllegalArgumentException("复习计划不存在");
        if (ReviewScheduleStatusEnum.COMPLETED.getValue().equals(reviewSchedule.getStatus()))
            throw new IllegalStateException("复习计划状态不正确，当前状态: " + reviewSchedule.getStatus());

        if (CollUtil.isEmpty(reviewSchedule.getTextbookItemIds())) {
            ThreadUtil.execAsync(() -> {
                reviewScheduleService.lambdaUpdate()
                        .set(ReviewSchedule::getReviewCourseId, course.getId())
                        .set(ReviewSchedule::getReviewByOneself, false)
                        .set(ReviewSchedule::getStatus, ReviewScheduleStatusEnum.SKIPPED.getValue())
                        .eq(ReviewSchedule::getId, reviewId)
                        .ne(ReviewSchedule::getStatus, ReviewScheduleStatusEnum.COMPLETED.getValue())
                        .update();
            });
            throw new IllegalArgumentException("没有可复习的单词，自动跳过复习");
        }

        // 判断是否之前已经开始过复习，如果状态为 进行中 ，则获取上次复习的环节和步骤，保持上次的进度
        if (reviewSchedule.getStatus().equals(ReviewScheduleStatusEnum.IN_PROGRESS.getValue())) {
            CourseSection courseSection = courseSectionService.lambdaQuery()
                    .eq(CourseSection::getType, CourseSectionTypeEnum.REVIEW.getValue())
                    .eq(CourseSection::getReviewScheduleId, reviewId)
                    .ge(DataEntity::getCreateTime, DateUtil.parseDate("2025-07-30"))
                    .orderByDesc(CourseSection::getCreateTime)
                    .last(" limit 1 ")
                    .one();
            if (courseSection != null) {
                log.info("复习计划已在进行中，复制上次复习进度数据: sectionId: {}", courseSection.getId());

                // 获取上次复习的步骤数据
                List<CourseSectionStep> originalSteps = courseSectionStepService.lambdaQuery()
                        .eq(CourseSectionStep::getSectionId, courseSection.getId())
                        .list();

                // 创建新的环节，复制上次的数据但生成新的ID
                String newSectionId = IdUtil.getSnowflakeNextIdStr();
                CourseSection newCourseSection = new CourseSection();
                BeanUtil.copyProperties(courseSection, newCourseSection);
                newCourseSection.setId(newSectionId);
                newCourseSection.setCourseId(course.getId());
                newCourseSection.setStatus("进行中");
                newCourseSection.setStartTime(WssContext.now());
                newCourseSection.setEndTime(null);
                newCourseSection.setCreateBy(null);
                newCourseSection.setCreateTime(null);
                newCourseSection.setUpdateBy(null);
                newCourseSection.setUpdateTime(null);

                // 创建新的步骤，复制上次的数据但生成新的ID
                List<CourseSectionStep> newSteps = originalSteps.stream().map(step -> {
                    CourseSectionStep newStep = new CourseSectionStep();
                    BeanUtil.copyProperties(step, newStep);
                    newStep.setId(IdUtil.getSnowflakeNextIdStr());
                    newStep.setSectionId(newSectionId);
                    newStep.setCourseId(course.getId());
                    // 保留原有的进度状态和结果，但清除结束时间
                    newStep.setEndTime(null);
                    newStep.setCreateBy(null);
                    newStep.setCreateTime(null);
                    newStep.setUpdateBy(null);
                    newStep.setUpdateTime(null);
                    return newStep;
                }).collect(Collectors.toList());

                // 保存新的环节和步骤
                courseSectionService.save(newCourseSection);
                courseSectionStepService.saveBatch(newSteps);

                course.setCurrentSectionIndex(course.getCurrentSectionIndex() + 1);

                // 构建响应数据，包含完整的复习进度信息
                CourseSectionDto courseSectionDto = buildCourseSectionDto(newCourseSection, newSteps);

                CourseDto courseDto = course.getContent() != null ? course.getContent().toBean(CourseDto.class) : initCourseDto(course);
                courseDto.getContent().setCurrentSectionIndex(course.getCurrentSectionIndex());
                courseDto.getContent().getSections().add(courseSectionDto);

                course.setContent(JSONUtil.parseObj(courseDto));


                lambdaUpdate().set(Course::getCurrentSectionIndex, course.getCurrentSectionIndex()).set(Course::getContent, course.getContent(), "typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler")
                        .eq(Course::getId, course.getId())
                        .update();


                // 更新复习计划的内容字段，记录当前环节信息
                reviewScheduleService.lambdaUpdate()
                        .set(ReviewSchedule::getReviewCourseId, course.getId())
                        .set(ReviewSchedule::getContent, JSONUtil.toJsonStr(courseSectionDto))
                        .eq(ReviewSchedule::getId, reviewId)
                        .update();

                CourseStartReviewDto.Resp resp = new CourseStartReviewDto.Resp();
                BeanUtil.copyProperties(courseSectionDto, resp);

                log.info("成功恢复抗遗忘复习进度, courseId: {}, newSectionId: {}, reviewId: {}", id, newSectionId, reviewId);
                return resp;
            }
        }


        List<WordLearnStepTypeEnum> stepTypes = getReviewStepTypes(reviewSchedule.getReviewType());
        List<String> textbookItemIds = Optional.ofNullable(reviewSchedule.getTextbookItemIds()).orElse(Collections.emptyList());

        WssContext.set("course::review::id", reviewId);

        CourseSectionDto courseSectionDto = createCourseSection(course, reviewSchedule.getName(), CourseSectionTypeEnum.REVIEW.getValue(), reviewId, textbookItemIds, stepTypes);

        reviewScheduleService.lambdaUpdate()
                .set(ReviewSchedule::getReviewCourseId, course.getId())
                .set(ReviewSchedule::getReviewByOneself, false)
                .set(ReviewSchedule::getStatus, ReviewScheduleStatusEnum.IN_PROGRESS.getValue())
                .set(ReviewSchedule::getActualStartTime, WssContext.now())
                .set(ReviewSchedule::getContent, JSONUtil.toJsonStr(courseSectionDto))
                .eq(ReviewSchedule::getId, reviewId)
                .eq(ReviewSchedule::getStatus, ReviewScheduleStatusEnum.WAIT_START.getValue())
                .update();

        CourseStartReviewDto.Resp resp = new CourseStartReviewDto.Resp();

        BeanUtil.copyProperties(courseSectionDto, resp);


        log.info("成功开始抗遗忘复习, courseId: {}, sectionId: {}, reviewId: {}", id, courseSectionDto.getId(), reviewId);
        return resp;
    }

    @Override
    public CourseStartReviewDto.Resp startEndReview(String id) throws IOException {
        log.info("开始下课前复习, courseId: {}", id);

        if (!StrUtil.isNotEmpty(id)) {
            throw new IllegalArgumentException("课程ID不能为空");
        }

        // 验证课程状态
        Course course = getById(id);
        checkCourseStatus(course);
        checkCourseTeacher(course);
        checkCourseType(course);

        // 判断是否已下课复习
        if (courseSectionService.lambdaQuery().eq(CourseSection::getCourseId, id).eq(CourseSection::getType, CourseSectionTypeEnum.CLASS_END_REVIEW.getValue()).exists()) {
            throw new IllegalArgumentException("本次课已完成过下课前复习，不能重复复习");
        }

        List<String> textbookItemIds = getCourseLearnedTextbookItemIds(id); //getCourseErrorTextbookItemIds(id);

        if (textbookItemIds.isEmpty()) {
            log.info("本次课程没有错误单词，无需下课前复习");

            throw new IllegalStateException("本次课程没有错误单词，无需下课前复习");
        }


        CourseSectionDto courseSectionDto = createCourseSection(course, "下课前复习", CourseSectionTypeEnum.CLASS_END_REVIEW.getValue(), null, textbookItemIds, WordLearnStepTypeEnum.forEndClass());

        // 构建返回对象
        CourseStartReviewDto.Resp resp = new CourseStartReviewDto.Resp();
        BeanUtil.copyProperties(courseSectionDto, resp);

        log.info("成功开始下课前复习, courseId: {}, sectionId: {}, 错误单词数: {}",
                id, courseSectionDto.getId(), textbookItemIds.size());
        return resp;
    }

    private CourseSectionDto createCourseSection(Course course, String title, String type, String sourceId, List<String> textbookItemIds, List<WordLearnStepTypeEnum> stepTypes) throws IOException {

        // 获取当前课程的环节索引
        if (course.getCurrentSectionIndex() == null) {
            course.setCurrentSectionIndex(-1L);
        }

        course.setCurrentSectionIndex(course.getCurrentSectionIndex() + 1);

        CourseSection section = new CourseSection();
        section.setId(IdUtil.getSnowflakeNextIdStr());
        section.setCourseId(course.getId());
        section.setTitle(title);
        section.setType(type);
        section.setStatus("进行中");
        section.setOrderIndex(course.getCurrentSectionIndex());
        section.setCurrentWordIndex(0L);

        if (type.equals(CourseSectionTypeEnum.REVIEW.getValue())) {
//            section.setReviewScheduleId(WssContext.get("course::review::id"));
            section.setReviewScheduleId(sourceId);
        }else if(type.equals(CourseSectionTypeEnum.WORD_TEST.getValue())){
            section.setReviewScheduleId(sourceId);
        }


        List<CourseSectionWordDto> courseSectionWordDtos = buildSectionWords(section, textbookItemIds, stepTypes);

        courseSectionService.save(section);


        CourseSectionDto courseSectionDto = new CourseSectionDto();
        courseSectionDto.setId(section.getId());
        courseSectionDto.setTitle(section.getTitle());
        courseSectionDto.setType(section.getType());
        courseSectionDto.setStatus(section.getStatus());
        courseSectionDto.setCurrentWordIndex(section.getCurrentWordIndex());
        courseSectionDto.setStartTime(section.getStartTime());
        courseSectionDto.setEndTime(section.getEndTime());
        courseSectionDto.setWords(courseSectionWordDtos);

        CourseDto courseDto = course.getContent() != null ? course.getContent().toBean(CourseDto.class) : initCourseDto(course);
        courseDto.getContent().setCurrentSectionIndex(course.getCurrentSectionIndex());
        courseDto.getContent().getSections().add(courseSectionDto);

        course.setContent(JSONUtil.parseObj(courseDto));


        lambdaUpdate().set(Course::getCurrentSectionIndex, course.getCurrentSectionIndex()).set(Course::getContent, course.getContent(), "typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler")
                .eq(Course::getId, course.getId())
                .update();

        return courseSectionDto;
    }

    /**
     * 构建课程环节DTO，用于恢复复习进度
     *
     * @param courseSection 课程环节
     * @param steps 环节步骤列表
     * @return CourseSectionDto
     */
    private CourseSectionDto buildCourseSectionDto(CourseSection courseSection, List<CourseSectionStep> steps) {
        CourseSectionDto courseSectionDto = new CourseSectionDto();
        courseSectionDto.setId(courseSection.getId());
        courseSectionDto.setTitle(courseSection.getTitle());
        courseSectionDto.setType(courseSection.getType());
        courseSectionDto.setStatus(courseSection.getStatus());
        courseSectionDto.setCurrentWordIndex(courseSection.getCurrentWordIndex());
        courseSectionDto.setStartTime(courseSection.getStartTime());
        courseSectionDto.setEndTime(courseSection.getEndTime());

        // 按textbookItemId分组步骤
        Map<String, List<CourseSectionStep>> stepsByTextbookItem = steps.stream()
                .collect(Collectors.groupingBy(CourseSectionStep::getTextbookItemId));

        // 获取所有textbookItemId
        List<String> textbookItemIds = steps.stream()
                .map(CourseSectionStep::getTextbookItemId)
                .distinct()
                .toList();

        // 构建单词信息
        List<CourseSectionWordInfo> wordInfos = courseSection.getWords().getBeanList("wordInfos", CourseSectionWordInfo.class);

        // 创建textbookItemId到wordInfo的映射
        Map<String, CourseSectionWordInfo> wordInfoMap = new HashMap<>();
        for (int i = 0; i < textbookItemIds.size() && i < wordInfos.size(); i++) {
            wordInfoMap.put(textbookItemIds.get(i), wordInfos.get(i));
        }

        // 构建单词DTO列表
        List<CourseSectionWordDto> wordDtos = new ArrayList<>();
        for (String textbookItemId : textbookItemIds) {
            List<CourseSectionStep> wordSteps = stepsByTextbookItem.get(textbookItemId);
            CourseSectionWordInfo wordInfo = wordInfoMap.get(textbookItemId);

            if (wordSteps != null && wordInfo != null) {
                CourseSectionWordDto wordDto = new CourseSectionWordDto();
                wordDto.setId(wordInfo.getId()); // 使用wordInfo的id字段，这是wordId
                wordDto.setWordInfo(wordInfo);


                // 构建步骤DTO列表
                List<CourseSectionStepDto> stepDtos = wordSteps.stream()
                        .sorted(Comparator.comparing(CourseSectionStep::getOrderIndex))
                        .map(step -> {
                            CourseSectionStepDto stepDto = new CourseSectionStepDto();
                            BeanUtil.copyProperties(step, stepDto);
                            stepDto.setOptions(step.getOptionsData());
                            stepDto.setSentenceOrder(step.getOrdering());
                            return stepDto;
                        })
                        .collect(Collectors.toList());

                wordDto.setSteps(stepDtos);

                // 设置单词状态和当前步骤索引
                long completedSteps = stepDtos.stream()
                        .mapToLong(s -> "已完成".equals(s.getStatus()) ? 1 : 0)
                        .sum();

                if (completedSteps == stepDtos.size()) {
                    wordDto.setStatus("已完成");
                    wordDto.setCurrentStepIndex(stepDtos.size() - 1);
                } else {
                    wordDto.setStatus("进行中");
                    // 找到第一个未完成的步骤索引
                    for (int i = 0; i < stepDtos.size(); i++) {
                        if (!"已完成".equals(stepDtos.get(i).getStatus())) {
                            log.info("设置当前步骤索引: textbookItemId={}, stepIndex={}", textbookItemId, i);
                            wordDto.setCurrentStepIndex(i);
                            break;
                        }
                    }
                }

                wordDtos.add(wordDto);
            }
        }

        courseSectionDto.setWords(wordDtos);
        return courseSectionDto;
    }

    /**
     * 查询本次课程中所有出现错误的单词项
     *
     * @param id
     * @return
     */
    private List<String> getCourseErrorTextbookItemIds(String id) {
        // 查询本次课程中所有出现错误的单词
        return courseSectionStepService.lambdaQuery()
                .select(CourseSectionStep::getTextbookItemId)
                .eq(CourseSectionStep::getCourseId, id)
                .eq(CourseSectionStep::getResult, "错误")
                .list().stream().map(CourseSectionStep::getTextbookItemId).distinct().toList();
    }

    /**
     * 查询本次课程中所有出现错误的单词列表
     *
     * @param courseId 课程ID
     * @return 错误单词列表
     */
    private List<String> getCourseErrorWords(String courseId) {
        try {
            if (StrUtil.isBlank(courseId)) {
                log.warn("课程ID为空，无法获取错误单词列表");
                return new ArrayList<>();
            }

            // 1. 查询本次课程中所有出现错误的教材项ID
            List<String> errorTextbookItemIds = courseSectionStepService.lambdaQuery()
                    .select(CourseSectionStep::getTextbookItemId)
                    .eq(CourseSectionStep::getCourseId, courseId)
                    .eq(CourseSectionStep::getResult, "错误")
                    .list().stream()
                    .map(CourseSectionStep::getTextbookItemId)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            if (CollUtil.isEmpty(errorTextbookItemIds)) {
                log.debug("课程{}没有错误的教材项", courseId);
                return new ArrayList<>();
            }

            // 2. 根据教材项ID查询对应的单词ID
            List<String> wordIds = textbookItemService.lambdaQuery()
                    .select(TextbookItem::getWordId)
                    .in(TextbookItem::getId, errorTextbookItemIds)
                    .list().stream()
                    .map(TextbookItem::getWordId)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            if (CollUtil.isEmpty(wordIds)) {
                log.debug("课程{}的错误教材项没有对应的单词ID", courseId);
                return new ArrayList<>();
            }

            // 3. 根据单词ID查询单词文本
            List<String> errorWords = wordService.lambdaQuery()
                    .select(Word::getWord)
                    .in(Word::getId, wordIds)
                    .list().stream()
                    .map(Word::getWord)
                    .filter(Objects::nonNull)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();

            log.debug("课程{}获取到{}个错误单词", courseId, errorWords.size());
            return errorWords;

        } catch (Exception e) {
            log.error("获取课程错误单词列表异常, courseId: {}", courseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询本次课程中所有学过的的单词项
     *
     * @param id
     * @return
     */
    private List<String> getCourseLearnedTextbookItemIds(String id) {
        // 查询本次课程中所有出现错误的单词
        List<String> learnSectionIds = courseSectionService.lambdaQuery().select(CourseSection::getId).eq(CourseSection::getCourseId, id).eq(CourseSection::getType, LEARNING.getValue()).list().stream().map(CourseSection::getId).toList();
        if (CollUtil.isEmpty(learnSectionIds)) {
            return new ArrayList<>();
        }
        return courseSectionStepService.lambdaQuery()
                .select(CourseSectionStep::getTextbookItemId)
                .eq(CourseSectionStep::getCourseId, id)
                .in(CourseSectionStep::getSectionId, learnSectionIds)
                .isNotNull(CourseSectionStep::getResult)
                .list().stream().map(CourseSectionStep::getTextbookItemId).distinct().toList();
    }

    @Override
    public void endCourse(String id) {
        log.info("结束课程, courseId: {}", id);

        // 获取课程完整信息
        Course course = getById(id);
        if (course == null) {
            throw new IllegalArgumentException("课程不存在");
        }

        checkCourseTeacher(course);

        if (!Objects.equals(course.getCourseStatus(), "进行中")) {
            log.warn("【结束课程】 课程不是进行中，不能结束课程，当前状态: {}", course.getCourseStatus());
            throw new IllegalArgumentException("课程不是进行中，不能结束课程，当前状态: " + course.getCourseStatus());
        }

        course.setCourseStatus("已完成");
        course.setActualEndTime(WssContext.now());
        course.setUpdateTime(WssContext.now());

        // 课消逻辑：只有正式课才需要课消
        if ("学习课".equals(course.getType())) {
            if (courseHoursConfigUtil.isCourseHoursEnabled()) {
                course.setConsumptionMethod("自动消课");
                performCourseHoursConsumption(course);
            } else {
                log.info("课消功能已关闭，跳过课消: courseId={}", id);
            }
        }

        // 课程异常标签
        {
            // 1. 迟到5分钟以上
            if (course.getActualStartTime().after(DateUtil.offsetMinute(course.getScheduledStartTime(), 5))) {
                course.addExceptionType("不准时");
                course.addExceptionType("迟到");
            }
            // 2. 早退5分钟以上
            if (course.getActualEndTime().before(DateUtil.offsetMinute(course.getScheduledEndTime(), -5))) {
                course.addExceptionType("不准时");
                course.addExceptionType("早退");
            }
            // 3. 时长不足计划的90%
            if (Math.abs(DateUtil.betweenMs(course.getActualStartTime(), course.getActualEndTime())) < course.getDurationMinutes() * 0.9) {
                course.addExceptionType("时长不足");
            }

        }

        // 不是单词课，直接更新课程状态和结束时间
        if (!StrUtil.equals("单词课", course.getSpecification())) {
            updateById(course);
            return;
        }

        // 计算课程统计信息
        courseStatistics(id, course);

        List<CourseSection> sections = courseSectionService.selectByCourseId(id);
        for (CourseSection section : sections) {
            sectionStatistics(section);

            if ("进行中".equals(section.getStatus())) {
                section.setStatus("已完成");
                section.setEndTime(WssContext.now());
            }

            courseSectionService.updateById(section);


            // 处理抗遗忘复习完成动作，所有步骤都完成才需要处理
            if (CourseSectionTypeEnum.REVIEW.getValue().equals(section.getType()) && StrUtil.isNotEmpty(section.getReviewScheduleId())) {
                boolean allStepsCompleted = isAllStepsCompleted(section.getId());
                if (allStepsCompleted) {
                    completeReviewSchedule(section);
                }
            }


            // 结束所有进行中的step
//            courseSectionStepService.lambdaUpdate()
//                    .set(CourseSectionStep::getStatus, "已完成")
//                    .set(CourseSectionStep::getResult, "错误")
//                    .set(CourseSectionStep::getStudentAnswer, null)
//                    .eq(CourseSectionStep::getCourseId, id)
//                    .in(CourseSectionStep::getStatus, "待开始", "进行中")
//                    .update();

        }

        // 固化content内容
        CourseDto courseDto = buildCourseDto(course);
        course.setContent(JSONUtil.parseObj(courseDto));

        // 更新课程
        updateById(course);

        if (course.getType().equals("学习课")) {
            List<String> courseErrorTextbookItemIds = getCourseLearnedTextbookItemIds(course.getId());
            if (CollUtil.isNotEmpty(courseErrorTextbookItemIds)) {

                // 生成抗遗忘复习计划
                reviewScheduleService.generateReviewSchedules(course.getStudentId(), course.getId(), course.getActualEndTime(), courseErrorTextbookItemIds);


                // 生成复习课
//                try {
//                    generateReviewCourse(course);
//                } catch (ParseException e) {
//                    throw new RuntimeException("生产复习课失败", e);
//                }
            }
        }
        ThreadPoolService.getThreadPool().execute(() -> {
            generatePdf(courseDto.getContent().getSections().stream().filter(item -> LEARNING.getValue().equals(item.getType())).toList(), courseDto.getId());
        });

        // 生成错误内容PDF并保存下载地址
        ThreadPoolService.submit(() -> generateAndSaveErrorMaterials(id));

        // 组装下课消息推送
        ThreadPoolService.getThreadPool().execute(() -> {
            switch (course.getType()) {
                case "学习课" -> wxSendMessageService.generalEndCourseWxMessage(course);
                case "复习课" -> wxSendMessageService.generalEndReviewWxMessage(course);
                default -> {
                }
            }
        });

        log.info("成功结束课程, courseId: {}", id);
    }

    /**
     * 执行课消逻辑
     * 只有正式课才需要课消，根据subject、specification、nature匹配课时
     * 按order_time先进先扣（FIFO），先扣购买，购买不够再扣赠送
     * 按排课时长进行消课，1课时=60分钟
     *
     * @param course 课程信息
     */
    @Override
    public void performCourseHoursConsumption(Course course) {
        try {
            log.info("开始执行课消逻辑: courseId={}, studentId={}, subject={}, specification={}, type={}, duration={}",
                    course.getId(), course.getStudentId(), course.getSubject(),
                    course.getSpecification(), course.getType(), course.getDurationMinutes());

            // 验证课程信息
            if (course.getStudentId() == null) {
                log.warn("课程学生ID为空，跳过课消: courseId={}", course.getId());
                return;
            }

            if (StrUtil.isBlank(course.getSubject()) || StrUtil.isBlank(course.getSpecification())) {
                log.warn("课程学科或课型为空，跳过课消: courseId={}, subject={}, specification={}",
                        course.getId(), course.getSubject(), course.getSpecification());
                return;
            }

            // 计算消费课时数：按排课时长计算，1课时=60分钟
            Long durationMinutes = course.getDurationMinutes();
            if (durationMinutes == null || durationMinutes <= 0) {
                log.warn("课程时长无效，跳过课消: courseId={}, duration={}", course.getId(), durationMinutes);
                return;
            }

            // 计算消费课时数，保留2位小数
            BigDecimal consumedHours = BigDecimal.valueOf(durationMinutes)
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);

            log.info("计算课消课时数: courseId={}, duration={}分钟, consumedHours={}课时",
                    course.getId(), durationMinutes, consumedHours);

            // 执行课时消费（会自动记录课消记录，包含课程性质匹配）
            boolean consumeSuccess = studentCourseHoursService.consumeHours(
                    course.getStudentId(),
                    course.getSubject(),
                    course.getSpecification(),
                    course.getCourseType(), // 传递课程性质（试听课、正式课）
                    consumedHours,
                    course.getId(), // 传递课程ID
                    course.getTeacherId(),
                    StrUtil.format("{}，课程时长{}分钟", "手工消课".equals(course.getConsumptionMethod()) ? "手工消课":"课程结束自动课消",durationMinutes)
            );

            if (!consumeSuccess) {
                log.error("课时消费失败: courseId={}, studentId={}, subject={}, specification={}, consumedHours={}",
                        course.getId(), course.getStudentId(), course.getSubject(),
                        course.getSpecification(), consumedHours);
                throw new RuntimeException("课时余额不足或课消失败");
            }

            log.info("课消执行成功: courseId={}, studentId={}, consumedHours={}",
                    course.getId(), course.getStudentId(), consumedHours);

        } catch (Exception e) {
            log.error("课消执行失败: courseId={}, error={}", course.getId(), e.getMessage(), e);
            throw new RuntimeException("课消执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成复习课
     *
     * @param course
     * @throws ParseException
     */
    private void generateReviewCourse(Course course) throws ParseException {
        log.debug("生成复习课, courseId: {}, studentId: {}, subject: {}, specification: {}, teacherId: {}",
                course.getId(), course.getStudentId(), course.getSubject(), course.getSpecification(), course.getTeacherId());
        int[] days = new int[]{2, 4, 7, 14, 21};
        List<String> dates = new ArrayList<>();
        for (int day : days) {
            dates.add(DateUtil.format(DateUtil.offsetDay(course.getScheduledStartTime(), day), DatePattern.NORM_DATE_PATTERN));
        }


        List<Course> courses = lambdaQuery().select(Course::getScheduledStartTime)
                .eq(Course::getStudentId, course.getStudentId())
                .eq(Course::getSubject, course.getSubject())
                .eq(Course::getSpecification, course.getSpecification())
                .eq(Course::getTeacherId, course.getTeacherId())
                .eq(Course::getCourseStatus, "待开始")
                .eq(Course::getType, "复习课")
                .list();
        List<String> existDates = courses.stream().map(x -> DateUtil.format(x.getScheduledStartTime(), DatePattern.NORM_DATE_PATTERN)).toList();

        dates.removeAll(existDates);

        if (dates.isEmpty()) {
            return;
        }

        List<Course> newCourses = new ArrayList<>();

        for (String day : dates) {
            log.debug("生成 {} 复习课", day);
            Course c = new Course();
            c.setStudentId(course.getStudentId());
            c.setTeacherId(course.getTeacherId());
            c.setCourseStatus("待开始");
            c.setSubject(course.getSubject());
            c.setSpecification(course.getSpecification());
            c.setType("复习课");
            c.setDurationMinutes(0L);
            c.setScheduledStartTime(DatePattern.NORM_DATE_FORMAT.parse(day));
            c.setScheduledEndTime(DatePattern.NORM_DATE_FORMAT.parse(day));

            newCourses.add(c);
        }

        saveBatch(newCourses);

    }


    private void courseStatistics(String courseId, Course course) {
        // 统计总单词数、学习单词数、错误单词数等
        List<CourseSectionStep> allSteps = courseSectionStepService.lambdaQuery().eq(CourseSectionStep::getCourseId, course.getId()).list();


        Set<String> allWords = allSteps.stream().map(CourseSectionStep::getWordId).collect(Collectors.toSet());
        Set<String> learnedWords = allSteps.stream()
                .filter(step -> !"待开始".equals(step.getStatus()))
                .map(CourseSectionStep::getTextbookItemId)
                .collect(Collectors.toSet());
        long mistakeCount = allSteps.stream()
                .filter(step -> "错误".equals(step.getResult()))
                .map(CourseSectionStep::getTextbookItemId)
                .distinct()
                .count();

        course.setStatWordCnt((long) allWords.size());
        course.setStatWordLearnCnt((long) learnedWords.size());
        course.setStatWordMistakesCnt(mistakeCount);
    }

    private void sectionStatistics(CourseSection section) {


        List<CourseSectionStep> allSteps = courseSectionStepService.lambdaQuery().eq(CourseSectionStep::getSectionId, section.getId()).list();

        Set<String> allWords = allSteps.stream().map(CourseSectionStep::getWordId).collect(Collectors.toSet());
        Set<String> learnedWords = allSteps.stream()
                .filter(step -> !"待开始".equals(step.getStatus()))
                .map(CourseSectionStep::getTextbookItemId)
                .collect(Collectors.toSet());
        long errorCount = allSteps.stream()
                .filter(step -> "错误".equals(step.getResult()))
                .map(CourseSectionStep::getTextbookItemId)
                .distinct()
                .count();

        section.setStatTotalWords((long) allWords.size());
        section.setStatLearnedWords((long) learnedWords.size());
        section.setStatErrorWords(errorCount);
        section.setStatCorrectWords(section.getStatLearnedWords() - section.getStatErrorWords());
    }

    @Override
    public void courseStepSubmit(String stepId, CourseStepSubmitDto.Req req) {
        log.info("提交课程学习步骤结果, stepId: {}, result: {}", stepId, req.getResult());

        if (!StrUtil.isNotEmpty(stepId)) {
            throw new IllegalArgumentException("步骤ID不能为空");
        }

        CourseSectionStep step = courseSectionStepService.getById(stepId);

        if (step.getStatus().equals("已完成") || step.getStatus().equals("已取消")) {
            log.warn("步骤已完成或取消, stepId: {}, status: {}", stepId, step.getStatus());
            return;
        }

        Course course = getById(step.getCourseId());
        checkCourseStatus(course);

        checkCourseTeacher(course);

        CourseSection section = courseSectionService.getById(step.getSectionId());
        if (section.getStatus().equals("已完成")) {
            log.warn("环节已完成, sectionId: {}, status: {}", step.getSectionId(), section.getStatus());
            return;
        }

        String result = req.getResult();
        String status = "已完成";

        boolean updateResult = courseSectionStepService.updateStepResult(stepId, status, result, req.getStudentAnswer());
        if (!updateResult) {
            throw new RuntimeException("更新学习步骤结果失败");
        }

        // 更新课程和环节的进度
        updateLearningProgress(course.getStudentId(), step.getCourseId(), step.getSectionId(), step.getTextbookItemId(), step.getWord(), result);

        log.info("成功提交课程学习步骤结果, stepId: {}, result: {}", stepId, result);
    }

    /**
     * 更新单词测验进度
     */
    private void updateWordTestProgress(String studentId, String courseId, String result, String word, boolean allWordCompleted) {

        StudentWordTest studentWordTest = studentWordTestService.lambdaQuery()
                .eq(StudentWordTest::getStudentId, studentId)
                .eq(StudentWordTest::getCourseId, courseId)
                .eq(StudentWordTest::getStatus, WordTestStatusEnum.IN_PROGRESS.getValue())
                .orderByDesc(StudentWordTest::getCreateTime)
                .last("limit 1")
                .one();

        if ("错误".equals(result)) {
            if (CollectionUtil.isEmpty(studentWordTest.getWrongWord())) {
                studentWordTest.setWrongWord(new ArrayList<>());
            }
            // 避免重复添加同一个单词
            if (!studentWordTest.getWrongWord().contains(word)) {
                studentWordTest.getWrongWord().add(word);
            }
        } else {
            if (CollectionUtil.isEmpty(studentWordTest.getCorrectWord())) {
                studentWordTest.setCorrectWord(new ArrayList<>());
            }
            // 避免重复添加同一个单词
            if (!studentWordTest.getCorrectWord().contains(word)) {
                studentWordTest.getCorrectWord().add(word);
            }
            studentWordTest.setSuccessWordNum(studentWordTest.getSuccessWordNum() + 1);
        }

        if (allWordCompleted) {
            int successWordNum = studentWordTest.getSuccessWordNum();
            int totalWordNum = studentWordTest.getTestedWordNum();

            double collectRate = (double) successWordNum / totalWordNum * 100;
            String collectRateStr = String.format("%.1f", collectRate) + "%";

            Textbook textbook = textbookService.getById(studentWordTest.getTextbookId());
            String suggestionTextbook = textbook.getName();
            if (collectRate > 80) {
                int grade = textbook.getGrade();
                int semeter = textbook.getSemester();
                String publisher = textbook.getPublisher();
                if (!TextBookTypeEnum.SCHOOL.getValue().equals(textbook.getType())) {
                    Textbook tarTextbook = textbookService.lambdaQuery().eq(StrUtil.isNotEmpty(publisher), Textbook::getPublisher, publisher).gt(Textbook::getGrade, grade).orderByAsc(Textbook::getGrade).last("limit 1").one();
                    suggestionTextbook = tarTextbook == null ? suggestionTextbook : tarTextbook.getName();
                } else {
                    Textbook tarTextbook = textbookService.lambdaQuery().eq(StrUtil.isNotEmpty(publisher), Textbook::getPublisher, publisher).eq(Textbook::getGrade, grade).gt(Textbook::getSemester, semeter).orderByAsc(Textbook::getSemester).last("limit 1").one();
                    if (tarTextbook == null) {
                        tarTextbook = textbookService.lambdaQuery().eq(StrUtil.isNotEmpty(publisher), Textbook::getPublisher, publisher).eq(Textbook::getGrade, grade + 1).orderByAsc(Textbook::getSemester).last("limit 1").one();
                    }
                    suggestionTextbook = tarTextbook == null ? suggestionTextbook : tarTextbook.getName();
                }

            }

            String consumTime = calcConsumTime(studentWordTest.getCreateTime());

            studentWordTest.setStatus(WordTestStatusEnum.COMPLETED.getValue())
                    .setSuggestions(suggestionTextbook)
                    .setSuccessRate(collectRateStr)
                    .setConsumTime(consumTime);

            // 异步生成错误内容材料
            if (!CollectionUtil.isEmpty(studentWordTest.getWrongWord())) {
                ThreadPoolService.getThreadPool().execute(() -> {
                    generateAndSaveWordTestErrorMaterials(studentWordTest.getId());
                });
            }
        }

        studentWordTestService.updateById(studentWordTest);
    }

    private String calcConsumTime(Date createTime) {
        long diffInMillies = Math.abs(new Date().getTime() - createTime.getTime());
        return String.format("%s分%s秒", diffInMillies / 1000 / 60, diffInMillies / 1000 % 60);
    }

    /**
     * 更新单词测验进度
     */
    private void updateWordTestProgressBak(String studentId, String courseId, String result, String word, boolean allWordCompleted) {

        StudentWordTest studentWordTest = studentWordTestService.lambdaQuery()
                .eq(StudentWordTest::getStudentId, studentId)
                .eq(StudentWordTest::getCourseId, courseId)
                .eq(StudentWordTest::getStatus, WordTestStatusEnum.IN_PROGRESS.getValue())
                .orderByDesc(StudentWordTest::getCreateTime)
                .last("limit 1")
                .one();

        if ("错误".equals(result)) {
            if (CollectionUtil.isEmpty(studentWordTest.getWrongWord())) {
                studentWordTest.setWrongWord(new ArrayList<>());
            }
            // 避免重复添加同一个单词
            if (!studentWordTest.getWrongWord().contains(word)) {
                studentWordTest.getWrongWord().add(word);
            }
        } else {
            if (CollectionUtil.isEmpty(studentWordTest.getCorrectWord())) {
                studentWordTest.setCorrectWord(new ArrayList<>());
            }
            // 避免重复添加同一个单词
            if (!studentWordTest.getCorrectWord().contains(word)) {
                studentWordTest.getCorrectWord().add(word);
            }
            studentWordTest.setSuccessWordNum(studentWordTest.getSuccessWordNum() + 1);
        }

        int estimatedWordNum = 0;
        String testResult = "";
        String collectRate = "";
        String suggestions = "";
        if (allWordCompleted) {
            int successWordNum = "正确".equals(result) ? studentWordTest.getSuccessWordNum() : studentWordTest.getSuccessWordNum() + 1;
            int totalWordNum = studentWordTest.getTestedWordNum();
            List<String> textbookIds = textbookService.lambdaQuery()
                    .eq(Textbook::getStage, studentWordTest.getLastGradeInfo().getStage())
                    .list().stream().map(Textbook::getId).toList();

            long allWordNum = textbookItemService.lambdaQuery()
                    .in(TextbookItem::getTextbookId, textbookIds)
                    .list()
                    .stream().map(TextbookItem::getWordId).distinct().count();

            //计算词汇水平和预估词汇量
            double percent = (double) successWordNum / totalWordNum * 100;
            collectRate = String.format("%.1f", percent) + "%";
            estimatedWordNum = (int) Math.ceil((double) successWordNum / totalWordNum * allWordNum);

            if (percent < 60) {
                testResult = "低";
            } else if (percent >= 85) {
                testResult = "高";
            } else {
                testResult = "中";
            }

            /**
             * 2.	你的课本词汇掌握率约为：%（保留1位小数）
             * 规则：（1）课本单词掌握率<80%：你的课本单词掌握率还有很大提升空间噢，建议和老师一起快速搞定课本单词库！
             * （2）课本单词掌握率>80%：你的课本单词掌握率还不错噢，不过还是要再接再厉，争取做到100%！
             * 3.	你的本阶段核心单词掌握率：%（保留1位小数）
             * 规则：（1）特色词库单词掌握率<80%：你的核心高分单词掌握率还有很大提升空间噢，建议和老师一起快速搞定这些高分必备词汇！
             * （2）特色词库单词掌握率>80%：你的高分必备词汇掌握率还不错噢，不过还是要再接再厉，争取做到100%，然后就可以挑战下个难度等级的词汇啦！
             */
            long lastSemesterCollectWordNum = studentWordTest.getSuccessWordNum() - studentWordTest.getCorrectWord().size();
            double lastSemesterPercent = (double) lastSemesterCollectWordNum / studentWordTest.getLastSemesterWord().size() * 100;
            String lastSemesterCollectRate = String.format("%.1f", lastSemesterPercent) + "%";
            String lastSemesterSuggestions;

            if (lastSemesterPercent >= 80) {
                lastSemesterSuggestions = "你的课本单词掌握率还不错噢，不过还是要再接再厉，争取做到100%！";
            } else {
                lastSemesterSuggestions = "你的课本单词掌握率还有很大提升空间噢，建议和老师一起快速搞定课本单词库！";
            }

            int specificCollectWordNum = studentWordTest.getCorrectWord().size() - (int) lastSemesterCollectWordNum;
            double specificCollectPercent = (double) specificCollectWordNum / studentWordTest.getSpecialWord().size() * 100;
            String specificCollectRate = String.format("%.1f", specificCollectPercent) + "%";
            String specificSuggestions;

            if (specificCollectPercent >= 80) {
                specificSuggestions = "你的高分必备词汇掌握率还不错噢，不过还是要再接再厉，争取做到100%，然后就可以挑战下个难度等级的词汇啦！";
            } else {
                specificSuggestions = "你的核心高分单词掌握率还有很大提升空间噢，建议和老师一起快速搞定这些高分必备词汇！";
            }

            /**
             * 的英语学习计划 （1）建议教材：课本单词；XX词库。
             * （2）	建议每周2-3课时
             * 好的开始就是成功的一半！学习也可以很有趣，我们一定加油成为英语王者！
             * 课时建议规则：1.课本单词<60%，2-6月（非寒暑假），每周3-4课时；60-80%，每周2-3课时；>90%, 每周2-3课时。
             * 2. 寒暑假：1.课本单词<60%，寒暑假，每周5-6课时；60-80%，每周4-5课时；>90%, 每周3-4课时。
             * 2.特色词汇>90%, 建议下阶段特色词库。
             */
            String suggestTime;
            String specialWordLearnSuggestions = "";
            LocalDate localDate = LocalDate.now();
            int month = localDate.getMonthValue();
            if (month > 6 && month < 9) {
                // 暑假
                if (lastSemesterPercent < 60) {
                    suggestTime = "每周5-6课时";
                } else if (lastSemesterPercent >= 60 && lastSemesterPercent < 80) {
                    suggestTime = "每周4-5课时";
                } else {
                    suggestTime = "每周3-4课时";
                }
            } else {
                // 非寒暑假
                if (lastSemesterPercent < 60) {
                    suggestTime = "每周3-4课时";
                } else {
                    suggestTime = "每周2-3课时";
                }
            }

            if (specificCollectPercent >= 90) {
                String stage = studentWordTest.getLastGradeInfo().getStage();
                specialWordLearnSuggestions = "小学".equals(stage) ? "1278词库" : "初中".equals(stage) ? "2222词库" : "";
            }

            StudentWordTest.TestDetailInfo testDetailInfo = new StudentWordTest.TestDetailInfo()
                    .setLastSemesterWordCollectRate(lastSemesterCollectRate)
                    .setLastSemesterSuggestions(lastSemesterSuggestions)
                    .setSpecialWordCollectRate(specificCollectRate)
                    .setSpecialWordSuggestions(specificSuggestions)
                    .setSuggestLearnTime(suggestTime)
                    .setSpecialWordLearnSuggestions(specialWordLearnSuggestions)
                    .setSuggestTextbookName(studentWordTest.getLastGradeInfo().getTextbookName());

            studentWordTest.setStatus(WordTestStatusEnum.COMPLETED.getValue())
                    .setEstimatedWordNum(estimatedWordNum)
                    .setResult(testResult)
                    .setTestDetailInfo(testDetailInfo)
                    .setSuccessRate(collectRate);
        }

        studentWordTestService.updateById(studentWordTest);
    }

    /**
     * 更新本课学习进度
     *
     * @param courseId       课程ID
     * @param sectionId      环节ID
     * @param textbookItemId 教材单元ID
     * @param word
     * @param result
     */
    private void updateLearningProgress(String studentId, String courseId, String sectionId, String textbookItemId, String word, String result) {
        // 检查当前单词的所有步骤是否完成
        List<CourseSectionStep> wordSteps = courseSectionStepService.list(
                new LambdaQueryWrapper<CourseSectionStep>()
                        .eq(CourseSectionStep::getSectionId, sectionId)
                        .eq(CourseSectionStep::getTextbookItemId, textbookItemId));

        boolean allCompleted = wordSteps.stream()
                .noneMatch(step -> "待开始".equals(step.getStatus()));

        if (allCompleted) {
            // 单词学习完成，移动到下一个单词
            boolean allWordCompleted = courseSectionStepService.lambdaQuery()
                    .select(CourseSectionStep::getTextbookItemId, CourseSectionStep::getStatus, CourseSectionStep::getResult)
                    .eq(CourseSectionStep::getSectionId, sectionId).list()
                    .stream().collect(Collectors.groupingBy(CourseSectionStep::getTextbookItemId))
                    .entrySet().stream().noneMatch(e -> e.getValue().stream().allMatch(x -> StrUtil.isEmpty(x.getResult())));

            CourseSection section = courseSectionService.getById(sectionId);

            if (allWordCompleted) {
                sectionStatistics(section);
                section.setStatus("已完成");
                // 处理抗遗忘复习
                if (CourseSectionTypeEnum.REVIEW.getValue().equals(section.getType())) {
                    // 完成复习计划
                    completeReviewSchedule(section);
                }
            } else {
                section.setCurrentWordIndex(section.getCurrentWordIndex() + 1);
            }

            courseSectionService.updateById(section);

            // 更新学生总体学习进度
            updateStudentLearnProgress(courseId, textbookItemId, wordSteps.stream().noneMatch(x -> StrUtil.equals(x.getResult(), "错误")));


            if (CourseSectionTypeEnum.WORD_TEST.getValue().equals(section.getType())) {
                //更新单词测验进度
                updateWordTestProgress(studentId, courseId, result, word, allWordCompleted);
            }
        }
    }

    /**
     * 完成抗遗忘复习
     *
     * @param section
     */
    private void completeReviewSchedule(CourseSection section) {
        String reviewId = section.getReviewScheduleId();
        String reviewSectionId = section.getId();
        String reviewCourseId = section.getCourseId();
        // 更新复习计划状态为已完成
        ReviewSchedule reviewSchedule = reviewScheduleService.getById(reviewId);
        if (reviewSchedule == null) {
            log.error("抗遗忘复习计划不存在, reviewId: {}", reviewId);
            return;
        }

        // 构建复习计划内容
        Map<String, Object> contentMap = new HashMap<>();

        // 获取所有步骤
        List<CourseSectionStep> steps = courseSectionStepService.lambdaQuery()
                .select(CourseSectionStep::getTextbookItemId, CourseSectionStep::getResult, CourseSectionStep::getStatus)
                .eq(CourseSectionStep::getSectionId, reviewSectionId)
                .list();

        // 统计总单词数、正确单词数、错误单词数
        long totalWords = steps.stream().map(CourseSectionStep::getTextbookItemId).distinct().count();

        long totalSteps = steps.size();
        long correctSteps = steps.stream().filter(step -> "正确".equals(step.getResult())).count();
        long wrongSteps = steps.stream().filter(step -> "错误".equals(step.getResult())).count();

        contentMap.put("totalWords", totalWords);
        contentMap.put("correctWords", section.getStatCorrectWords());
        contentMap.put("wrongWords", section.getStatErrorWords());
        contentMap.put("completedTime", WssContext.now());

        reviewSchedule.setStatus(ReviewScheduleStatusEnum.COMPLETED.getValue());
        reviewSchedule.setActualEndTime(WssContext.now());
        reviewSchedule.setReviewCourseId(reviewCourseId);
        reviewSchedule.setReviewByUserId(WssContext.userId());
        reviewSchedule.setReviewByOneself(false);
        reviewSchedule.setContent(JSONUtil.toJsonStr(contentMap));
        reviewSchedule.setStatWordTotal(totalWords);
        reviewSchedule.setStatWordCorrect(section.getStatCorrectWords());
        reviewSchedule.setStatWordIncorrect(section.getStatErrorWords());
        reviewSchedule.setStatStepCorrect(correctSteps);
        reviewSchedule.setStatStepIncorrect(wrongSteps);
        reviewSchedule.setStatStepTotal(totalSteps);

        reviewScheduleService.updateById(reviewSchedule);

        log.info("完成抗遗忘复习计划, reviewId: {}, 总单词: {}, 正确: {}, 错误: {}",
                reviewId, totalWords, section.getStatCorrectWords(), section.getStatErrorWords());
    }

    @Override
    public void awardPoints(String id, CoursePointsAwardDto.Req req) {

        StudentPointTransaction spt = new StudentPointTransaction();
        Course course = getById(id);
        checkCourseType(course);

        spt.setId(IdUtil.getSnowflakeNextIdStr());
        spt.setStudentId(course.getStudentId());
        spt.setCreateBy(WssContext.userId());
        spt.setTransactionType(PointTransactionTypeEnum.REWARD.getValue());
        spt.setSourceCategory(PointTransactionSourceCategoryEnum.TEACHER_REWARD.getValue());
        spt.setPointsChange(req.getPoints());
        spt.setPointsBalanceAfter(0L); // 这里应该查询学生当前积分余额
        spt.setSourceEntityId(id);
        spt.setSourceSubcategory(req.getType());
        spt.setTransactionTime(WssContext.now());

        studentPointTransactionService.save(spt);


    }

    @Override
    public CourseGetReviewDto.Resp getReviewList(String studentId) {
        // 查询学生的复习计划列表
        // 这里应该查询复习计划表，暂时返回mock数据
        CourseGetReviewDto.Resp resp = new CourseGetReviewDto.Resp();

        List<ReviewSchedule> reviewSchedules = reviewScheduleService.list(new QueryWrapper<ReviewSchedule>().lambda()
                .eq(ReviewSchedule::getStudentId, studentId)
                .notIn(ReviewSchedule::getStatus, Arrays.asList(ReviewScheduleStatusEnum.COMPLETED.getValue(), ReviewScheduleStatusEnum.SKIPPED.getValue()))
                .orderByAsc(ReviewSchedule::getScheduledTime)
                .orderByAsc(ReviewSchedule::getCreateTime));

        Map<String, List<ReviewSchedule>> reviewScheduleMap = reviewSchedules.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getScheduledTime(), "yyyy-MM-dd")
                , TreeMap::new, Collectors.toList()));
        Iterator<Map.Entry<String, List<ReviewSchedule>>> iterator = reviewScheduleMap.entrySet().iterator();
        List<CourseGetReviewDto.Resp.Review> reviews = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<String, List<ReviewSchedule>> entry = iterator.next();
            CourseGetReviewDto.Resp.Review review = new CourseGetReviewDto.Resp.Review();
            review.setStudentId(studentId);
            review.setScheduledTime(entry.getKey());
            List<CourseGetReviewDto.Resp.DetailContent> contents = entry.getValue().stream().map(item -> {
                CourseGetReviewDto.Resp.DetailContent detailContent = new CourseGetReviewDto.Resp.DetailContent();
                BeanUtils.copyProperties(item, detailContent);
                detailContent.setWordCount(item.getStatWordTotal());
                return detailContent;
            }).toList();
            review.setDetailContents(contents);

            reviews.add(review);
        }
        resp.setReviews(reviews);
        return resp;
    }

    /**
     * 下课生成单词pdf
     *
     * @param sectionDtoList
     */
    @Override
    public void generatePdf(List<CourseSectionDto> sectionDtoList, String courseId) {


        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String PDFName = "【北大军哥名师团神奇英语】" + formatted + "单词讲义";
        List<CourseSectionWordInfo> wordDtoList = new ArrayList<>();
        sectionDtoList.stream().map(CourseSectionDto::getWords).toList().forEach(item -> {
            item.forEach(i -> wordDtoList.add(i.getWordInfo()));
        });

        String wordInfoUrl = "";
        try {
            ByteArrayOutputStream baos = generateWordInfoPdf(wordDtoList, PDFName);
            String objectName = StrUtil.format("course/{}/{}.pdf", courseId, PDFName);

            log.info("PDF 文件已生成：{}", objectName);
            wordInfoUrl = ossService.uploadBytes(baos.toByteArray(), objectName, OssService.newFilenameMetadata(PDFName + ".pdf"));
        } catch (Exception e) {
            log.error("生成单词讲义PDF失败: {}", e.getMessage(), e);
        }


        PDFName = "【北大军哥名师团神奇英语】" + formatted + "单词练习一";
        String practicesInfoUrl = generatePracticesInfoPdf(sectionDtoList, courseId
                , "1. 看音标，拼读一遍，写出单词（尽自己能力写，不确定的可以看讲义，但是要用红笔进行订正，目的是找到自己的记忆重点），然后复习中文释义，再写一遍单词加深记忆。如果有错误，可再订正3遍。"
                , "2. 看句子翻译，对照讲义抄写1遍英文句子（也可以直接默写，然后红笔订正，找到记忆重点）。"
                , PDFName);

        PDFName = "【北大军哥名师团神奇英语】" + formatted + "单词练习二";
        String practicesInfoUrl1 = generatePracticesInfoPdf(sectionDtoList, courseId
                , "1. 看音标，拼读一遍，写出单词，复习中文释义，再写一遍单词；如果有错误，可再订正3遍。"
                , "2. 看句子翻译，默写1遍英文（能写多少写多少，写不出来的直接对照讲义红笔订正，找到记忆重点）。"
                , PDFName);

        this.update(new UpdateWrapper<Course>().lambda()
                .set(Course::getWordPdfUrl, wordInfoUrl)
                .set(Course::getPracticesPdfUrl, practicesInfoUrl)
                .set(Course::getPracticesPdfUrl1, practicesInfoUrl1)
                .eq(Course::getId, courseId));

        log.info("保存课程pdf信息成功:{}", courseId);

    }

    /**
     * 开始单词测试
     *
     * @param studentId   学生ID
     * @param courseId    课程ID
     * @param wordTestDto
     * @return WordTestDto
     */
    @Override
    public CourseSectionDto startTest(String studentId, String courseId, CourseWordTestDto wordTestDto) throws IOException {
        Course course = getById(courseId);

        if (Objects.isNull(course)) {
            throw new IllegalArgumentException("课程不存在");
        }

        if (!"进行中".equals(course.getCourseStatus())) {
            throw new IllegalStateException("课程状态不正确，当前状态: " + course.getCourseStatus());
        }


        // 2. 获取教材单词
        StudentWordTest studentWordTest = new StudentWordTest();
        studentWordTest.setId(IdUtil.getSnowflakeNextIdStr());

        List<TextbookItem> textbookItems;
        if ("range".equals(wordTestDto.getTestMode()) && wordTestDto.getStartIndex() != null && wordTestDto.getEndIndex() != null) {
            // 范围选择模式
            textbookItems = textbookItemService.lambdaQuery()
                    .eq(TextbookItem::getTextbookId, wordTestDto.getTextbookId())
                    .isNotNull(TextbookItem::getWordId)
                    .orderByAsc(TextbookItem::getDisplayOrder)
                    .last("LIMIT " + (wordTestDto.getEndIndex() - wordTestDto.getStartIndex() + 1) + " OFFSET " + (wordTestDto.getStartIndex() - 1))
                    .list();
        } else {
            // 随机选择模式（默认）
            textbookItems = textbookItemService.lambdaQuery()
                    .eq(TextbookItem::getTextbookId, wordTestDto.getTextbookId())
                    .isNotNull(TextbookItem::getWordId)
                    .last("ORDER BY RANDOM() LIMIT " + wordTestDto.getWordNum())
                    .list();
        }

        CourseSectionDto courseSectionDto = createCourseSection(course, "词汇测验", CourseSectionTypeEnum.WORD_TEST.getValue(), studentWordTest.getId(), textbookItems.stream().map(TextbookItem::getId).toList(), List.of(WordLearnStepTypeEnum.WORD_PRACTICE));

        int wordNum = courseSectionDto.getWords().size();

        studentWordTest.setStudentId(studentId)
                .setCourseId(courseId)
                .setTestedWordNum(wordNum)
                .setSuccessWordNum(0)
                .setEstimatedWordNum(0)
                .setStatus(WordTestStatusEnum.IN_PROGRESS.getValue())
                .setContent(courseSectionDto)
                .setCorrectWord(new ArrayList<>())
                .setWrongWord(new ArrayList<>())
                .setTeacherId(course.getTeacherId())
                .setTextbookId(wordTestDto.getTextbookId())
        ;
        studentWordTestService.save(studentWordTest);

        log.info("成功开始单词测验 studentId:{},courseId: {}, sectionId: {}", studentId, courseId, courseSectionDto.getId());
        return courseSectionDto;
    }


    public CourseSectionDto startTestBak(String studentId, String courseId, CourseWordTestDto wordTestDto) throws IOException {
        Course course = getById(courseId);

        if (Objects.isNull(course)) {
            throw new IllegalArgumentException("课程不存在");
        }

        if (!"进行中".equals(course.getCourseStatus())) {
            throw new IllegalStateException("课程状态不正确，当前状态: " + course.getCourseStatus());
        }

        // 1. 获取学生信息
        UserStudentExt userStudentExt = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .orderByDesc(UserStudentExt::getGrade)
                .orderByDesc(UserStudentExt::getSemester)
                .last("limit 1")
                .one();

        String grade = null;
        Integer semester = null;
        String publisher = null;
        if (Objects.nonNull(userStudentExt)) {
            grade = userStudentExt.getGrade();
            semester = userStudentExt.getSemester();
            publisher = userStudentExt.getEnglishPublisher();
        }


        // 2. 获取上学期教材单词
        StudentWordTest studentWordTest = new StudentWordTest();
        studentWordTest.setId(IdUtil.getSnowflakeNextIdStr());
        studentWordTest.setLastGradeInfo(new StudentWordTest.LastGradeInfo());
        String stage = grade == null ? "小学" : GradeEnum.toStage(Integer.valueOf(grade));
        Textbook textbook = getTextbooks(grade, semester, publisher, studentWordTest);
        studentWordTest.getLastGradeInfo()
                .setTextbookId(textbook.getId())
                .setTextbookName(textbook.getName())
                .setStage(stage);
        List<TextbookItem> textbookItemIds = getTextbookWords(textbook, stage);
        List<String> lastSemesterWords = wordService.lambdaQuery().in(Word::getId, textbookItemIds.stream().map(TextbookItem::getWordId).toList()).list()
                .stream().map(Word::getWord).toList();

        // 3. 获取额外词库单词
        List<TextbookItem> extraItemIds = getExtraWords(stage, textbookItemIds);
        List<String> specialWords = wordService.lambdaQuery().in(Word::getId, extraItemIds.stream().map(TextbookItem::getWordId).toList()).list()
                .stream().map(Word::getWord).toList();
        textbookItemIds.addAll(extraItemIds);

        CourseSectionDto courseSectionDto = createCourseSection(course, "词汇测验", CourseSectionTypeEnum.WORD_TEST.getValue(), studentWordTest.getId(), textbookItemIds.stream().map(TextbookItem::getId).toList(), List.of(WordLearnStepTypeEnum.WORD_PRACTICE));

        int wordNum = courseSectionDto.getWords().size();

        studentWordTest.setStudentId(studentId)
                .setCourseId(courseId)
                .setTestedWordNum(wordNum)
                .setSuccessWordNum(0)
                .setEstimatedWordNum(0)
                .setStatus(WordTestStatusEnum.IN_PROGRESS.getValue())
                .setContent(courseSectionDto)
                .setCorrectWord(new ArrayList<>())
                .setWrongWord(new ArrayList<>())
                .setTeacherId(course.getTeacherId())
                .setLastSemesterWord(lastSemesterWords)
                .setSpecialWord(specialWords)
        ;
        studentWordTestService.save(studentWordTest);

        log.info("成功开始单词测验 studentId:{},courseId: {}, sectionId: {}", studentId, courseId, courseSectionDto.getId());
        return courseSectionDto;
    }

    /**
     * 获取学生的所有单词测验结果
     *
     * @param studentId
     * @return
     */
    @Override
    public List<WordTestDto> getWordTestList(String studentId) {
        List<StudentWordTest> studentWordTestList = studentWordTestService.lambdaQuery()
                .eq(StudentWordTest::getStudentId, studentId)
                .orderByDesc(StudentWordTest::getCreateTime)
                .list();

        if (CollectionUtil.isEmpty(studentWordTestList)) {
            return new ArrayList<>();
        }
        List<WordTestDto> ret = BeanUtil.copyToList(studentWordTestList, WordTestDto.class);
        ret.forEach(item -> {
            item.setTestedTime(DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATE_PATTERN));
            // 错误内容下载地址已通过BeanUtil.copyToList自动复制
            // 如果需要额外处理，可以在这里添加逻辑
        });

        return ret.stream().sorted(Comparator.comparing(WordTestDto::getCreateTime).reversed()).toList();
    }

    /**
     * 获取课程的最新测验结果
     *
     * @param courseId
     * @return
     */
    @Override
    public WordTestDto getWordByCourseId(String courseId) {
        StudentWordTest studentWordTest = studentWordTestService.lambdaQuery()
                .eq(StudentWordTest::getCourseId, courseId)
                .orderByDesc(StudentWordTest::getCreateTime)
                .last("limit 1")
                .one();
        WordTestDto wordTestDto = new WordTestDto();
        if (studentWordTest != null) {
            BeanUtil.copyProperties(studentWordTest, wordTestDto);
        }
        return wordTestDto;
    }

    @Override
    public void endSection(String sectionId) {
        CourseSection section = courseSectionService.getById(sectionId);
        if (section == null) {
            log.warn("结束环节失败，环节不存在，sectionId: {}", sectionId);
            throw new IllegalArgumentException("环节不存在");
        }

        Course course = getById(section.getCourseId());

        if (!course.getCourseStatus().equals("进行中")) {
            log.warn("结束环节失败，课程状态不是进行中，courseId: {}", course.getId());
            throw new IllegalArgumentException("当前课程不能结束环节");
        }

        if (!section.getStatus().equals("进行中")) {
            log.warn("结束环节失败，环节状态不是进行中，sectionId: {}", sectionId);
            throw new IllegalArgumentException("当前环节不能结束");
        }

        boolean allStepsCompleted = isAllStepsCompleted(sectionId);

        // 结束当前环节所有进行中的step
//        courseSectionStepService.lambdaUpdate()
//                .set(CourseSectionStep::getStatus, "已完成")
//                .set(CourseSectionStep::getResult, "错误")
//                .set(CourseSectionStep::getStudentAnswer, null)
//                .eq(CourseSectionStep::getSectionId, section.getId())
//                .in(CourseSectionStep::getStatus, "待开始", "进行中")
//                .update();

        sectionStatistics(section);
        section.setStatus("已完成");
        section.setEndTime(WssContext.now());
        courseSectionService.updateById(section);


        // 处理抗遗忘复习，不是所有完成，不能把抗遗忘复习设置为已完成
        if (allStepsCompleted && CourseSectionTypeEnum.REVIEW.getValue().equals(section.getType())) {
            // 完成复习计划
            completeReviewSchedule(section);
        }
    }

    /**
     * 判断环节所有步骤是否都已完成
     *
     * @param sectionId
     * @return
     */
    private boolean isAllStepsCompleted(String sectionId) {
        return courseSectionStepService.lambdaQuery()
                .eq(CourseSectionStep::getSectionId, sectionId)
                .in(CourseSectionStep::getStatus, "待开始", "进行中")
                .count() == 0;
    }

    @Override
    public List<Course> getTomorrowCourseList() {
        return this.baseMapper.getTomorrowCourseList();
    }

    @Override
    public List<Course> getToday12MinCourseList() {
        return this.baseMapper.getToday12MinCourseList();
    }

    @Override
    public ByteArrayOutputStream downloadPdf(List<String> textbookItemIds, boolean showDisplayOrder, String courseId, String PDFName) {
        List<CourseSectionWordInfo> wordInfos = buildWords(textbookItemIds, showDisplayOrder);
        return generateWordInfoPdf(wordInfos, PDFName);
    }



    /**
     * 获取教材
     */
    private Textbook getTextbooks(String gradeStr, Integer semester, String publisher, StudentWordTest studentWordTest) {
        log.info("传入年级信息：{}，学期：{}，出版社：{}", gradeStr, semester, publisher);
        AtomicReference<Textbook> textbookRef = new AtomicReference<>();
        if (StrUtil.isNotEmpty(gradeStr)) {
            int grade = Integer.parseInt(gradeStr);
            while (textbookRef.get() == null && grade > 0) {
                SemesterEnum semesterEnum = semester == null ? SemesterEnum.NULL : SemesterEnum.fromValue(semester);
                GradeEnum gradeEnum = GradeEnum.fromValue(grade);
                GradeEnum targetGrade = gradeEnum;
                switch (semesterEnum) {
                    case SEMESTER_UP -> {
                        semesterEnum = SemesterEnum.SEMESTER_DOWN;
                        targetGrade = GradeEnum.fromValue(gradeEnum.getValue() - 1);
                    }
                    case SEMESTER_DOWN -> {
                        semesterEnum = SemesterEnum.SEMESTER_UP;
                    }
                    case NULL, SEMESTER_ALL -> {
                        // 处理NULL和SEMESTER_ALL情况
                        // 保持原有值不变
                    }
                }

                // 填充取到的教材信息
//                studentWordTest.getLastGradeInfo().setLastGrade(targetGrade.getValue())
//                        .setLastSemester(semesterEnum.getValue())
//                        .setPublisher(publisher);
                boolean hasSemester = semesterEnum != SemesterEnum.NULL;
                if (!Objects.equals(targetGrade, GradeEnum.NULL)) {
                    String defaultPublisher = getDefaultPublisherName(GradeEnum.toStage(targetGrade.getValue()));
                    List<Textbook> textbooks = textbookService.lambdaQuery()
                            .eq(Textbook::getGrade, targetGrade.getValue())
                            .ne(Objects.equals(gradeEnum, targetGrade) && !Objects.equals(semesterEnum, SemesterEnum.NULL), Textbook::getSemester, semester)
                            .orderByDesc(hasSemester, Textbook::getSemester)
                            .orderByAsc(!hasSemester, Textbook::getSemester)
                            .list();
                    if (CollectionUtil.isNotEmpty(textbooks)) {
                        textbooks.stream().filter(item -> StrUtil.isNotEmpty(item.getPublisher()) && item.getPublisher().equals(publisher))
                                .findFirst()
                                .ifPresent(textbookRef::set);

                        if (textbookRef.get() == null) {
                            log.info("用到了默认出版社：{}", defaultPublisher);
                            textbooks.stream().filter(item -> StrUtil.isNotEmpty(item.getPublisher()) && item.getPublisher().equals(defaultPublisher))
                                    .findFirst()
                                    .ifPresent(textbookRef::set);
                        }
                    }
                } else {
                    break;
                }

                if (textbookRef.get() != null) {
                    log.info("取到年级信息：{}，学期：{}，出版社：{}", targetGrade.getValue(), semesterEnum.getValue(), publisher);
                }


                grade = semesterEnum == SemesterEnum.NULL ? targetGrade.getValue() - 1 : targetGrade.getValue();
                semester = semesterEnum.getValue();
            }
        }

        // 如果没找到教材，使用兜底教材
        if (Objects.isNull(textbookRef.get())) {
            String defaultPublisherName = getDefaultPublisherName("小学");
            textbookRef.set(textbookService.lambdaQuery()
                    .eq(Textbook::getPublisher, defaultPublisherName)
                    .orderByAsc(Textbook::getGrade)
                    .orderByAsc(Textbook::getSemester)
                    .last("limit 1")
                    .one());
        }

        log.info("单词测验，当前年级信息：{}，学期：{}，出版社：{}，取到的教材信息：{}", gradeStr, semester, publisher, textbookRef.get().getName());

        return textbookRef.get();
    }

    /**
     * 获取教材单词
     */
    private List<TextbookItem> getTextbookWords(Textbook textbook, String stage) {
        if (textbook == null) {
            return new ArrayList<>();
        }

        List<TextbookItem> items = textbookItemService.lambdaQuery()
                .in(TextbookItem::getTextbookId, textbook.getId())
                .list();

        Map<String, String> wordId2NameMap = wordService.listByIds(items.stream().map(TextbookItem::getWordId).toList()).stream().collect(Collectors.toMap(Word::getId, word -> word.getWord()));
        // 按单词长度过滤
        List<TextbookItem> filteredItems = items.stream()
                .filter(item -> {
                    String wordName = wordId2NameMap.get(item.getWordId());
                    if (wordName != null) {
                        int len = wordName.length();
                        return "小学".equals(stage) ?
                                (len >= 5 && len <= 7) :
                                (len >= 7 && len <= 12);
                    }
                    return false;
                }).collect(Collectors.toList());

        //不足10个补齐
        if (filteredItems.size() < 10) {
            filteredItems.addAll(items.stream()
                    .filter(item -> {
                        String wordName = wordId2NameMap.get(item.getWordId());
                        if (wordName != null) {
                            int len = wordName.length();
                            return (len >= 5 && len <= 7);
                        }
                        return false;
                    }).toList());
        }
        // 随机选择指定数量的单词
        int count = getTextbookWordCount(stage);
        return randomSelect(filteredItems, count);
    }

    /**
     * 获取额外词库单词
     */
    private List<TextbookItem> getExtraWords(String stage, List<TextbookItem> textbookItemIds) {
        List<TextbookItem> result = new ArrayList<>();
        List<String> list = textbookItemIds.stream().map(TextbookItem::getWordId).collect(Collectors.toList());
        if ("小学".equals(stage)) {
            // 315词和350词各7个
            result.addAll(getSpecialVocabularyWords("315词", 7, 5, 7, list));
            list.addAll(result.stream().map(TextbookItem::getWordId).toList());
            result.addAll(getSpecialVocabularyWords("350词", 7, 5, 7, list));
        } else if ("初中".equals(stage)) {
            // 1278词10个
            result.addAll(getSpecialVocabularyWords("1278词", 10, 7, 12, list));
        } else if ("高中".equals(stage)) {
            // 1278词5个，2222词10个
            result.addAll(getSpecialVocabularyWords("1278词", 5, 7, 12, list));
            list.addAll(result.stream().map(TextbookItem::getWordId).toList());
            result.addAll(getSpecialVocabularyWords("2222词", 10, 7, 12, list));
        }

        return result;
    }

    /**
     * 获取特殊词库的单词
     */
    private List<TextbookItem> getSpecialVocabularyWords(String name, int count, int minLength, int maxLength, List<String> wordIdList) {
        // 获取特定词库
        List<Textbook> textbooks = textbookService.lambdaQuery()
                .eq(Textbook::getType, "2")
                .like(Textbook::getName, name)
                .list();

        if (textbooks.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取单词并按长度过滤
        List<TextbookItem> items = textbookItemService.lambdaQuery()
                .in(TextbookItem::getTextbookId, textbooks.stream().map(Textbook::getId).toList())
                .notIn(TextbookItem::getWordId, wordIdList)
                .list();

        Map<String, String> wordId2NameMap = wordService.listByIds(items.stream().map(TextbookItem::getWordId).toList()).stream().collect(Collectors.toMap(Word::getId, word -> word.getWord()));
        List<TextbookItem> filteredItems = items.stream()
                .filter(item -> {
                    String wordName = wordId2NameMap.get(item.getWordId());
                    return wordName != null &&
                            wordName.length() >= minLength &&
                            wordName.length() <= maxLength;
                }).collect(Collectors.toList());

        return randomSelect(filteredItems, count);
    }

    /**
     * 从列表中随机选择指定数量的项
     */
    private List<TextbookItem> randomSelect(List<TextbookItem> items, int count) {
        if (items.size() <= count) {
            return new ArrayList<>(items);
        }
        List<TextbookItem> copy = new ArrayList<>(items);
        Collections.shuffle(copy);
        return copy.subList(0, count);
    }

    /**
     * 获取课本单词数量
     */
    private int getTextbookWordCount(String grade) {
        return switch (grade) {
            case "小学" -> 6;
            case "初中" -> 10;
            case "高中" -> 5;
            default -> 0;
        };
    }

    /**
     * 获取默认教材
     */
    private String getDefaultPublisherName(String grade) {
        return switch (grade) {
            case "小学", "初中" -> "人教版PEP";
            case "高中" -> "冀教版";
            default -> throw new RuntimeException("未知年级");
        };
    }

    /**
     * 生成测验pdf
     *
     * @param sectionDtoList
     * @param courseId
     * @return
     */
    private String generatePracticesInfoPdf(List<CourseSectionDto> sectionDtoList, String courseId, String title1, String title2, String PDFName) {
        try {
            // 2. 创建PDF文档
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfDocument pdfDoc = new PdfDocument(new PdfWriter(baos));
            Document document = new Document(pdfDoc);
//            PdfWriter writer = new PdfWriter("D:\\锐捷\\网络安全规划\\practices"+titleIndex+".pdf");
//            PdfDocument pdfDoc = new PdfDocument(writer);
//            Document document = new Document(pdfDoc);

            // 3. 加载中文字体 (确保fonts目录下有simsun.ttc字体文件)
            PdfFont chineseFont = PdfFontFactory.createFont("fonts/NotoSansCJKsc-Regular.otf", PdfEncodings.IDENTITY_H);
            PdfFont defaultFont = PdfFontFactory.createFont("fonts/CharisSIL-Regular.ttf", PdfEncodings.IDENTITY_H);

            // 4. 添加标题

            Paragraph title = new Paragraph(PDFName)
                    .setFont(chineseFont)
                    .setFontSize(18)
                    .setBold()
                    .setTextAlignment(TextAlignment.CENTER);
            document.add(title);

            Paragraph tip1 = new Paragraph("【练习要求】")
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip1);

            Paragraph tip2 = new Paragraph(title1)
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);

            document.add(tip2);

            Paragraph tip3 = new Paragraph(title2)
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);

            document.add(tip3);

            // 5. 添加分隔线
            document.add(new Paragraph("\n"));
            String space = " ";
            AtomicInteger index = new AtomicInteger(1);
            sectionDtoList.forEach(section -> {
                List<CourseSectionWordDto> wordDtos = section.getWords();
                wordDtos.forEach(wordDto -> {
                    CourseSectionWordInfo wordInfo = wordDto.getWordInfo();
                    try {
                        Paragraph wordContent = new Paragraph(index.getAndIncrement() + "." + wordInfo.getPhoneticUs())
                                .setFont(defaultFont);
                        wordContent.add(new Text(space + "_______").setFont(chineseFont))
                        ;

                        for (Word.Meanings.Pos pos : wordInfo.getMeanings().getPos()) {
                            wordContent.add(new Text(space + pos.getPos() + "." + pos.getDef() + space).setFont(chineseFont));
                        }

                        wordContent.add(new Text(space + "_______" + space + "_______" + space + "_______" + space + "_______").setFont(chineseFont));
                        document.add(wordContent);

                        Word.Sentences sentence = wordInfo.getSentences();
                        Paragraph sentenceContent = new Paragraph(space + sentence.getSentenceCn() + "翻译英文：")
                                .setFont(chineseFont);
                        sentenceContent.add(new Text(space + "_________________________________________________").setFont(chineseFont));

                        document.add(sentenceContent);
                    } catch (Exception e) {
                        log.error("【生成单词】生成单词：{}失败", wordInfo.getWord(), e);
                    }

                });
            });


            // 7. 关闭文档
            document.close();
//            String objectName = "course/" + courseId + "/practicesInfo.pdf";
            String objectName = StrUtil.format("course/{}/{}.pdf", courseId, PDFName);
            log.info("测验PDF 文件已生成：{}", objectName);
//            return null;
            return ossService.uploadBytes(baos.toByteArray(), objectName, OssService.newFilenameMetadata(PDFName + ".pdf"));
        } catch (Exception e) {
            log.error("【生成单词】打开pdf-sdk失败", e);
        }
        return "";
    }

    /**
     * 生成练习PDF
     * @param sectionDtoList 课程环节列表
     * @param courseId 课程ID
     * @param title1 练习要求1
     * @param title2 练习要求2
     * @param pdfName PDF名称
     * @param showDisplayOrder 是否显示单词原始顺序
     * @return PDF字节流
     */
    @Override
    public ByteArrayOutputStream generatePracticesInfoPdf(List<CourseSectionDto> sectionDtoList, String courseId, String title1, String title2, String pdfName, boolean showDisplayOrder) {
        try {
            // 2. 创建PDF文档
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfDocument pdfDoc = new PdfDocument(new PdfWriter(baos));
            Document document = new Document(pdfDoc);

            // 3. 加载中文字体 (确保fonts目录下有字体文件)
            PdfFont chineseFont = PdfFontFactory.createFont("fonts/NotoSansCJKsc-Regular.otf", PdfEncodings.IDENTITY_H);
            PdfFont defaultFont = PdfFontFactory.createFont("fonts/CharisSIL-Regular.ttf", PdfEncodings.IDENTITY_H);

            // 4. 添加标题
            Paragraph title = new Paragraph(pdfName)
                    .setFont(chineseFont)
                    .setFontSize(18)
                    .setBold()
                    .setTextAlignment(TextAlignment.CENTER);
            document.add(title);

            Paragraph tip1 = new Paragraph("【练习要求】")
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip1);

            Paragraph tip2 = new Paragraph(title1)
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip2);

            Paragraph tip3 = new Paragraph(title2)
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip3);

            // 5. 添加分隔线
            document.add(new Paragraph("\n"));
            String space = " ";
            AtomicInteger index = new AtomicInteger(1);
            sectionDtoList.forEach(section -> {
                List<CourseSectionWordDto> wordDtos = section.getWords();
                wordDtos.forEach(wordDto -> {
                    CourseSectionWordInfo wordInfo = wordDto.getWordInfo();
                    try {
                        Paragraph wordContent = new Paragraph((showDisplayOrder ? wordDto.getWordInfo().getDisplayOrder() : index.getAndIncrement()) + "." + wordInfo.getPhoneticUs())
                                .setFont(defaultFont);
                        wordContent.add(new Text(space + "_______").setFont(chineseFont));

                        for (Word.Meanings.Pos pos : wordInfo.getMeanings().getPos()) {
                            wordContent.add(new Text(space + pos.getPos() + "." + pos.getDef() + space).setFont(chineseFont));
                        }

                        wordContent.add(new Text(space + "_______" + space + "_______" + space + "_______" + space + "_______").setFont(chineseFont));
                        document.add(wordContent);

                        Word.Sentences sentence = wordInfo.getSentences();
                        Paragraph sentenceContent = new Paragraph(space + sentence.getSentenceCn() + "翻译英文：")
                                .setFont(chineseFont);
                        sentenceContent.add(new Text(space + "_________________________________________________").setFont(chineseFont));

                        document.add(sentenceContent);
                    } catch (Exception e) {
                        log.error("【生成单词】生成单词：{}失败", wordInfo.getWord(), e);
                    }
                });
            });

            // 6. 关闭文档
            document.close();

            // 返回ByteArrayOutputStream而不是上传到OSS
            return baos;
        } catch (Exception e) {
            log.error("【生成单词】打开pdf-sdk失败", e);
            throw new RuntimeException("生成练习PDF失败");
        }
    }

    /**
     * 生成讲义pdf
     *
     * @param wordInfoList
     * @param PDFName
     * @return
     */
    private ByteArrayOutputStream generateWordInfoPdf(List<CourseSectionWordInfo> wordInfoList, String PDFName) {
        try {
            // 2. 创建PDF文档
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfDocument pdfDoc = new PdfDocument(new PdfWriter(baos));
            Document document = new Document(pdfDoc);
//            PdfWriter writer = new PdfWriter("D:\\锐捷\\网络安全规划\\wordInfo.pdf");
//            PdfDocument pdfDoc = new PdfDocument(writer);
//            Document document = new Document(pdfDoc);

            // 3. 加载中文字体 (确保fonts目录下有字体文件)
            PdfFont chineseFont = PdfFontFactory.createFont("fonts/NotoSansCJKsc-Regular.otf", PdfEncodings.IDENTITY_H);
            PdfFont defaultFont = PdfFontFactory.createFont("fonts/CharisSIL-Regular.ttf", PdfEncodings.IDENTITY_H);

            // 4. 添加标题

            Paragraph title = new Paragraph(PDFName)
                    .setFont(chineseFont)
                    .setFontColor(ColorConstants.RED)
                    .setFontSize(18)
                    .setBold()
                    .setTextAlignment(TextAlignment.CENTER);
            document.add(title);

            Paragraph tip1 = new Paragraph("【抄写笔记方法】")
                    .setFontColor(ColorConstants.RED);
            tip1.add(new Text("尽量使用").setFontColor(ColorConstants.BLACK))
                    .add(new Text("蓝色").setFontColor(ColorConstants.BLUE))
                    .add(new Text("或").setFontColor(ColorConstants.BLACK))
                    .add(new Text("红色").setFontColor(ColorConstants.RED))
                    .add(new Text("等彩色水笔， 让笔记更加醒目重点突出，方便复习。").setFontColor(ColorConstants.BLACK))
            ;
            tip1.setFont(chineseFont).setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip1);

            Paragraph tip2 = new Paragraph("重点笔记内容：（1）划分单词音节；（2）用括号划分句子结构，抄写老师讲解的重点知识。")
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip2);

            Paragraph tip3 = new Paragraph("音标说明：第1个是英式；第2个是美式。")
                    .setFont(chineseFont)
                    .setFontSize(10)
                    .setBold()
                    .setTextAlignment(TextAlignment.LEFT);
            document.add(tip3);

            // 5. 添加分隔线
            document.add(new Paragraph("\n"));
            String space = " ";

            AtomicInteger index = new AtomicInteger(1);

            wordInfoList.forEach(wordInfo -> {
                try {

                    // 单词标题（加粗）
                    int idx = index.getAndIncrement();
                    int displayOrder = wordInfo.getDisplayOrder() != null ? wordInfo.getDisplayOrder() : idx;
                    Paragraph wordContent = new Paragraph(displayOrder + ". " + wordInfo.getWord())
                            .setFont(chineseFont);
                    wordContent.add(new Text(space + wordInfo.getPhoneticUk()).setFont(defaultFont))
                            .add(new Text(space + wordInfo.getPhoneticUs()).setFont(defaultFont))
                    ;

                    for (Word.Meanings.Pos pos : wordInfo.getMeanings().getPos()) {
                        wordContent.add(new Text(space + pos.getPos() + "." + pos.getDef() + "    ").setFont(chineseFont));
                    }

                    // 例句信息
                    Word.Sentences sentence = wordInfo.getSentences();
                    wordContent.add(new Text(space + sentence.getSentenceEn()).setFont(chineseFont))
                            .add(new Text(space + sentence.getSentenceCn()).setFont(chineseFont));

                    document.add(wordContent);
                } catch (Exception e) {
                    log.error("【生成单词】生成单词：{}失败", wordInfo.getWord(), e);
                }

            });


            // 7. 关闭文档
            document.close();
//            return null;
            return baos;
        } catch (Exception e) {
            log.error("【生成单词】打开pdf-sdk失败", e);
        }
        return null;
    }

    @Override
    public ByteArrayOutputStream generateErrorHandoutPdf(String courseId) {
        log.info("生成课程错误内容讲义PDF, courseId: {}", courseId);

        // 1. 获取课程错误单词项ID列表
        List<String> errorTextbookItemIds = getCourseErrorTextbookItemIds(courseId);
        if (CollUtil.isEmpty(errorTextbookItemIds)) {
            log.warn("课程{}没有错误单词，无法生成错词讲义", courseId);
            return null;
        }

        // 2. 构建单词信息
        List<CourseSectionWordInfo> errorWordInfos = buildWords(errorTextbookItemIds, false);

        // 3. 生成PDF名称
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String pdfName = "【北大军哥名师团神奇英语】" + formatted + "错词讲义";

        // 4. 生成PDF
        return generateWordInfoPdf(errorWordInfos, pdfName);
    }

    @Override
    public ByteArrayOutputStream generateErrorExercisePdf(String courseId) {
        log.info("生成课程错误内容练习PDF, courseId: {}", courseId);

        // 1. 获取课程错误单词项ID列表
        List<String> errorTextbookItemIds = getCourseErrorTextbookItemIds(courseId);
        if (CollUtil.isEmpty(errorTextbookItemIds)) {
            log.warn("课程没有错误单词，无法生成错题练习");
            return null;
        }

        // 2. 构建错误单词的课程环节数据（用于练习生成）
        List<CourseSectionDto> errorSections = buildErrorSections(courseId, errorTextbookItemIds);

        // 3. 生成PDF名称
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String pdfName = "【北大军哥名师团神奇英语】" + formatted + "错题练习";

        // 4. 直接复用现有的课堂练习生成逻辑
        String title1 = "1. 看音标，拼读一遍，写出单词（尽自己能力写，不确定的可以看讲义，但是要用红笔进行订正，目的是找到自己的记忆重点），然后复习中文释义，再写一遍单词加深记忆。如果有错误，可再订正3遍。";
        String title2 = "2. 看句子翻译，对照讲义抄写1遍英文句子（也可以直接默写，然后红笔订正，找到记忆重点）。";
        return generatePracticesInfoPdf(errorSections, courseId, title1, title2, pdfName, false);
    }

    /**
     * 获取课程中实际答错的题目步骤
     */
    private List<CourseSectionStep> getCourseErrorSteps(String courseId) {
        // 查询本次课程中所有学习环节
        List<String> learnSectionIds = courseSectionService.lambdaQuery()
                .select(CourseSection::getId)
                .eq(CourseSection::getCourseId, courseId)
                .eq(CourseSection::getType, LEARNING.getValue())
                .list().stream().map(CourseSection::getId).toList();

        if (CollUtil.isEmpty(learnSectionIds)) {
            return new ArrayList<>();
        }

        // 查询所有答错的步骤，包含完整的题目信息
        return courseSectionStepService.lambdaQuery()
                .eq(CourseSectionStep::getCourseId, courseId)
                .in(CourseSectionStep::getSectionId, learnSectionIds)
                .eq(CourseSectionStep::getResult, "错误")
                .orderByAsc(CourseSectionStep::getTextbookItemId)
                .orderByAsc(CourseSectionStep::getOrderIndex)
                .list();
    }

    /**
     * 构建错误单词的课程环节数据
     */
    private List<CourseSectionDto> buildErrorSections(String courseId, List<String> errorTextbookItemIds) {
        // 构建单词信息
        List<CourseSectionWordInfo> errorWordInfos = buildWords(errorTextbookItemIds, false);

        // 创建一个虚拟的课程环节
        CourseSectionDto errorSection = new CourseSectionDto();
        errorSection.setId("error_section_" + courseId);
        errorSection.setType("学习");
        errorSection.setTitle("错误单词复习");

        // 转换为CourseSectionWordDto格式
        List<CourseSectionWordDto> errorWords = errorWordInfos.stream()
                .map(wordInfo -> {
                    CourseSectionWordDto wordDto = new CourseSectionWordDto();
                    wordDto.setWordInfo(wordInfo);
                    return wordDto;
                })
                .toList();

        errorSection.setWords(errorWords);

        return List.of(errorSection);
    }


    /**
     * 根据复习类型获取对应的步骤类型
     *
     * @param reviewType 复习类型 (D2, D4, D7, D14, D21)
     * @return 步骤类型列表
     */
    private List<WordLearnStepTypeEnum> getReviewStepTypes(String reviewType) {
        return switch (reviewType) {
            case "D2" -> WordLearnStepTypeEnum.forD2();
            case "D4" -> WordLearnStepTypeEnum.forD4();
            case "D7" -> WordLearnStepTypeEnum.forD7();
            case "D14" -> WordLearnStepTypeEnum.forD14();
            case "D21" -> WordLearnStepTypeEnum.forD21();
            default -> {
                // 默认使用D2的步骤类型
                log.warn("未知的复习类型: {}, 使用D2步骤类型", reviewType);
                yield WordLearnStepTypeEnum.forD2();
            }
        };
    }

    @Override
    public ByteArrayOutputStream generateWordTestErrorHandoutPdf(String studentWordTestId) {
        log.info("生成词汇测试错误内容讲义PDF, studentWordTestId: {}", studentWordTestId);

        // 1. 直接通过studentWordTestId获取错误单词的教材项ID
        List<String> errorTextbookItemIds = getWordTestErrorTextbookItemIds(studentWordTestId);
        if (CollUtil.isEmpty(errorTextbookItemIds)) {
            log.warn("单词测试{}没有错误单词，无法生成错词讲义", studentWordTestId);
            return null;
        }

        // 2. 构建单词信息
        List<CourseSectionWordInfo> errorWordInfos = buildWords(errorTextbookItemIds, false);

        // 3. 生成PDF名称
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String pdfName = "【北大军哥名师团神奇英语】" + formatted + "测试错词讲义";

        // 4. 生成PDF
        return generateWordInfoPdf(errorWordInfos, pdfName);
    }

    @Override
    public ByteArrayOutputStream generateWordTestErrorExercisePdf(String studentWordTestId) {
        log.info("生成词汇测试错误内容练习PDF, studentWordTestId: {}", studentWordTestId);

        // 1. 直接通过studentWordTestId获取错误单词的教材项ID
        List<String> errorTextbookItemIds = getWordTestErrorTextbookItemIds(studentWordTestId);
        if (CollUtil.isEmpty(errorTextbookItemIds)) {
            log.warn("单词测试{}没有错误单词，无法生成错题练习", studentWordTestId);
            return null;
        }

        // 2. 获取课程ID（用于构建错误单词的课程环节数据）
        StudentWordTest wordTest = studentWordTestService.getById(studentWordTestId);
        if (wordTest == null) {
            log.warn("单词测试{}不存在", studentWordTestId);
            return null;
        }
        String courseId = wordTest.getCourseId();

        // 3. 构建错误单词的课程环节数据（用于练习生成）
        List<CourseSectionDto> errorSections = buildErrorSectionsFromWords(courseId, errorTextbookItemIds);

        // 4. 生成PDF名称
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String formatted = today.format(formatter);
        String pdfName = "【北大军哥名师团神奇英语】" + formatted + "测试错题练习";

        // 5. 直接复用现有的课堂练习生成逻辑
        String title1 = "1. 看音标，拼读一遍，写出单词（尽自己能力写，不确定的可以看讲义，但是要用红笔进行订正，目的是找到自己的记忆重点），然后复习中文释义，再写一遍单词加深记忆。如果有错误，可再订正3遍。";
        String title2 = "2. 看句子翻译，对照讲义抄写1遍英文句子（也可以直接默写，然后红笔订正，找到记忆重点）。";
        return generatePracticesInfoPdf(errorSections, courseId, title1, title2, pdfName, true);
    }

    /**
     * 直接获取词汇测试错误单词的教材项ID
     * 改进：直接通过studentWordTestId获取对应测验的错误步骤数据
     */
    private List<String> getWordTestErrorTextbookItemIds(String studentWordTestId) {
        log.info("获取词汇测试错误单词的教材项ID, studentWordTestId: {}", studentWordTestId);

        // 1. 通过review_schedule_id关联查找对应的course_section
        CourseSection testSection = courseSectionService.lambdaQuery()
                .eq(CourseSection::getReviewScheduleId, studentWordTestId)
                .eq(CourseSection::getType, "词汇测验")
                .one();

        if (testSection == null) {
            log.info("单词测试{}没有找到对应的词汇测验section", studentWordTestId);
            return new ArrayList<>();
        }

        // 2. 查询该section下所有答错的步骤，获取教材项ID
        List<String> errorTextbookItemIds = courseSectionStepService.lambdaQuery()
                .select(CourseSectionStep::getTextbookItemId)
                .eq(CourseSectionStep::getSectionId, testSection.getId())
                .eq(CourseSectionStep::getResult, "错误")
                .list()
                .stream()
                .map(CourseSectionStep::getTextbookItemId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        log.info("单词测试{}的错误教材项ID: {}", studentWordTestId, errorTextbookItemIds);
        return errorTextbookItemIds;
    }

    /**
     * 根据教材项ID构建错误单词的课程环节数据
     */
    private List<CourseSectionDto> buildErrorSectionsFromWords(String courseId, List<String> errorTextbookItemIds) {
        // 构建单词信息
        List<CourseSectionWordInfo> errorWordInfos = buildWords(errorTextbookItemIds, false);

        // 创建一个虚拟的课程环节
        CourseSectionDto errorSection = new CourseSectionDto();
        errorSection.setId("word_test_error_section_" + courseId);
        errorSection.setType("测试");
        errorSection.setTitle("词汇测试错误单词复习");

        // 转换为CourseSectionWordDto格式
        List<CourseSectionWordDto> errorWords = errorWordInfos.stream()
                .map(wordInfo -> {
                    CourseSectionWordDto wordDto = new CourseSectionWordDto();
                    wordDto.setWordInfo(wordInfo);
                    return wordDto;
                })
                .toList();

        errorSection.setWords(errorWords);

        return List.of(errorSection);
    }

    @Override
    public CourseMaterialDownloadDto.DownloadInfo generateAndUploadCourseErrorMaterial(String courseId, String type) {
        try {
            ByteArrayOutputStream baos = null;
            String fileName = null;
            String objectName = null;


            Course course = getById(courseId);

            String formatted = DateUtil.format(course.getScheduledStartTime(), DatePattern.CHINESE_DATE_PATTERN);

            if ("error_handout".equals(type)) {
                baos = generateErrorHandoutPdf(courseId);
                if (baos == null) {
                    throw new RuntimeException("本次课程没有错误单词，无法生成错词讲义");
                }
                fileName = "【北大军哥名师团神奇英语】" + formatted + "错词讲义.pdf";
                objectName = StrUtil.format("course/{}/{}.pdf", courseId, fileName);
            } else if ("error_exercise".equals(type)) {
                baos = generateErrorExercisePdf(courseId);
                if (baos == null) {
                    throw new RuntimeException("本次课程没有错误单词，无法生成错题练习");
                }
                fileName = "【北大军哥名师团神奇英语】" + formatted + "错题练习.pdf";
                objectName = StrUtil.format("course/{}/{}.pdf", courseId, fileName);
            } else {
                throw new RuntimeException("错误的资料类型");
            }

            String downloadUrl = ossService.uploadBytes(baos.toByteArray(), objectName, OssService.newFilenameMetadata(fileName));

            CourseMaterialDownloadDto.DownloadInfo downloadInfo = new CourseMaterialDownloadDto.DownloadInfo();
            downloadInfo.setDownloadUrl(downloadUrl);
            downloadInfo.setFileName(fileName);

            return downloadInfo;
        } catch (Exception e) {
            log.error("生成课程错误内容失败, courseId: {}, type: {}", courseId, type, e);
            throw new RuntimeException("生成失败：" + e.getMessage());
        }
    }


    /**
     * 生成并保存错误内容材料
     * 在课程完成时自动生成错词讲义和错题练习，并保存下载地址到数据库
     *
     * @param courseId 课程ID
     */
    private void generateAndSaveErrorMaterials(String courseId) {
        try {
            log.info("开始生成课程错误内容材料, courseId: {}", courseId);

            Course course = getById(courseId);
            if (course == null) {
                log.warn("课程不存在，跳过错误内容生成, courseId: {}", courseId);
                return;
            }

            // 只有单词课才生成错误内容
            if (!"单词课".equals(course.getSpecification())) {
                log.info("非单词课，跳过错误内容生成, courseId: {}, specification: {}", courseId, course.getSpecification());
                return;
            }

            boolean hasErrorContent = false;
            String errorHandoutUrl = null;
            String errorExerciseUrl = null;

            // 生成错词讲义
            try {
                CourseMaterialDownloadDto.DownloadInfo handoutInfo = generateAndUploadCourseErrorMaterial(courseId, "error_handout");
                if (handoutInfo != null && StrUtil.isNotEmpty(handoutInfo.getDownloadUrl())) {
                    errorHandoutUrl = handoutInfo.getDownloadUrl();
                    hasErrorContent = true;
                    log.info("成功生成错词讲义, courseId: {}, url: {}", courseId, errorHandoutUrl);
                }
            } catch (Exception e) {
                log.warn("生成错词讲义失败, courseId: {}, error: {}", courseId, e.getMessage());
            }

            // 生成错题练习
            try {
                CourseMaterialDownloadDto.DownloadInfo exerciseInfo = generateAndUploadCourseErrorMaterial(courseId, "error_exercise");
                if (exerciseInfo != null && StrUtil.isNotEmpty(exerciseInfo.getDownloadUrl())) {
                    errorExerciseUrl = exerciseInfo.getDownloadUrl();
                    hasErrorContent = true;
                    log.info("成功生成错题练习, courseId: {}, url: {}", courseId, errorExerciseUrl);
                }
            } catch (Exception e) {
                log.warn("生成错题练习失败, courseId: {}, error: {}", courseId, e.getMessage());
            }

            // 更新课程记录，保存下载地址
            if (hasErrorContent) {
                boolean updateResult = lambdaUpdate()
                    .set(errorHandoutUrl != null, Course::getErrorHandoutPdfUrl, errorHandoutUrl)
                    .set(errorExerciseUrl != null, Course::getErrorExercisePdfUrl, errorExerciseUrl)
                    .eq(Course::getId, courseId)
                    .update();

                if (updateResult) {
                    log.info("成功保存错误内容下载地址, courseId: {}, handout: {}, exercise: {}",
                            courseId, errorHandoutUrl != null, errorExerciseUrl != null);
                } else {
                    log.error("保存错误内容下载地址失败, courseId: {}", courseId);
                }
            } else {
                log.info("本次课程没有错误内容，无需保存下载地址, courseId: {}", courseId);
            }

        } catch (Exception e) {
            log.error("生成课程错误内容材料失败, courseId: {}", courseId, e);
        }
    }

    /**
     * 生成并保存单词测验错误内容材料
     * 在单词测验完成时自动生成错词讲义和错题练习，并保存下载地址到数据库
     *
     * @param wordTestId 单词测验ID
     */
    private void generateAndSaveWordTestErrorMaterials(String wordTestId) {
        try {
            log.info("开始生成单词测验错误内容材料, wordTestId: {}", wordTestId);

            StudentWordTest wordTest = studentWordTestService.getById(wordTestId);
            if (wordTest == null) {
                log.warn("单词测验不存在，跳过错误内容生成, wordTestId: {}", wordTestId);
                return;
            }

            boolean hasErrorContent = false;
            String errorHandoutUrl = null;
            String errorExerciseUrl = null;

            // 生成错词讲义
            try {
                ByteArrayOutputStream bos = generateWordTestErrorHandoutPdf(wordTestId);
                if (bos != null) {
                    // 生成文件名和对象名
                    LocalDate today = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
                    String formatted = today.format(formatter);
                    String fileName = StrUtil.format("【北大军哥名师团神奇英语】{}词汇测试错词讲义_{}.pdf", formatted, wordTestId);
                    String objectName = StrUtil.format("course/{}/_{}.pdf", wordTest.getCourseId(), fileName);

                    // 上传到OSS
                    errorHandoutUrl = ossService.uploadBytes(bos.toByteArray(), objectName, OssService.newFilenameMetadata(fileName));
                    hasErrorContent = true;
                    log.info("成功生成并上传测验错词讲义, wordTestId: {}, url: {}", wordTestId, errorHandoutUrl);
                }
            } catch (Exception e) {
                log.warn("生成错词讲义失败, wordTestId: {}, error: {}", wordTestId, e.getMessage());
            }

            // 生成错题练习
            try {
                ByteArrayOutputStream bos = generateWordTestErrorExercisePdf(wordTestId);
                if (bos != null) {
                    // 生成文件名和对象名
                    LocalDate today = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
                    String formatted = today.format(formatter);
                    String fileName = StrUtil.format("【北大军哥名师团神奇英语】{}词汇测试错题练习_{}.pdf", formatted, wordTestId);
                    String objectName = StrUtil.format("course/{}/{}.pdf", wordTest.getCourseId(), fileName);

                    // 上传到OSS
                    errorExerciseUrl = ossService.uploadBytes(bos.toByteArray(), objectName, OssService.newFilenameMetadata(fileName));
                    hasErrorContent = true;
                    log.info("成功生成并上传测验错题练习, wordTestId: {}, url: {}", wordTestId, errorExerciseUrl);
                }
            } catch (Exception e) {
                log.warn("生成错题练习失败, wordTestId: {}, error: {}", wordTestId, e.getMessage());
            }

            // 更新单词测验记录，保存下载地址
            if (hasErrorContent) {
                boolean updateResult = studentWordTestService.lambdaUpdate()
                        .set(errorHandoutUrl != null, StudentWordTest::getErrorHandoutPdfUrl, errorHandoutUrl)
                        .set(errorExerciseUrl != null, StudentWordTest::getErrorExercisePdfUrl, errorExerciseUrl)
                        .eq(StudentWordTest::getId, wordTestId)
                        .update();

            }
        } catch (Exception e) {
            log.error("生成单词测验错误内容材料失败, wordTestId: {}", wordTestId, e);
            throw e;
        }
    }

}
