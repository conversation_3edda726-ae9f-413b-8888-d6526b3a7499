package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.itextpdf.io.source.ByteArrayOutputStream;
import org.nonamespace.word.server.domain.Course;
import org.nonamespace.word.server.dto.*;
import org.nonamespace.word.server.dto.course.CourseSectionDto;
import org.nonamespace.word.server.dto.course.CourseWordTestDto;
import org.nonamespace.word.server.dto.course.CourseMaterialDownloadDto;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * 课程Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface ICourseService extends IService<Course> {

    /**
     * 查询课程信息接口 (Get Course Info)
     * @param id
     * @param detail 是否需要详细信息
     * @return
     */
    CourseInfoDto.Resp courseInfo(String id, Boolean detail);

    /**
     * 开始上课接口 (Start Class)
     * @param id
     */
     void startCourse(String id);

    /**
     * 开始上课预检接口 (Start Class)
     * @param id
     */
    void preflightCourse(String id);


    /**
     * 开始新课程学习接口 (Start New Course Learning)
     * @param id
     * @param req
     * @return
     */
     CourseStartLearningDto.Resp startLearning(String id, CourseStartLearningDto.Req req) throws IOException;


    /**
     * 开始抗遗忘复习接口 (Start Anti-Forgetting Review)
     * @param id
     * @param reviewId
     * @return
     */
     CourseStartReviewDto.Resp startReview(String id, String reviewId) throws IOException;


    /**
     * 开始下课前复习接口 (Start Pre-End-of-Class Review)
     * @param id
     * @return
     */
     CourseStartReviewDto.Resp startEndReview(String id) throws IOException;


    /**
     * 下课接口 (End Class)
     * @param id
     */
     void endCourse(String id);


    void performCourseHoursConsumption(Course course);

    /**
     * 课程学习步骤结果提交
     * @param stepId
     * @param req
     */
     void courseStepSubmit(String stepId, CourseStepSubmitDto.Req req);


    /**
     * 积分派发
     * @param id
     * @param req
     */
     void awardPoints(String id, CoursePointsAwardDto.Req req);


    /**
     * 获取抗遗忘复习列表接口 (Get Anti-Forgetting Review List)
     * @param studentId
     * @return
     */
     CourseGetReviewDto.Resp getReviewList(String studentId);

     void generatePdf(List<CourseSectionDto> wordInfos,String courseId) throws FileNotFoundException;

    CourseSectionDto startTest(String studentId, String courseId, CourseWordTestDto wordTestDto) throws IOException;

    List<WordTestDto> getWordTestList(String studentId);

    WordTestDto getWordByCourseId(String courseId);

    /**
     * 结束环节接口 (End Section)
     * @param sectionId
     */
    void endSection(String sectionId);



    /**
     *
     * @return
     */
    List<Course> getTomorrowCourseList();

    /**
     *
     * @return
     */
    List<Course> getToday12MinCourseList();


    ByteArrayOutputStream downloadPdf(List<String> textbookItemIds, boolean showDisplayOrder, String courseId,String pdfName);

    /**
     * 生成课程错误内容讲义PDF
     * @param courseId 课程ID
     * @return PDF字节流
     */
    ByteArrayOutputStream generateErrorHandoutPdf(String courseId);

    /**
     * 生成课程错误内容练习PDF
     * @param courseId 课程ID
     * @return PDF字节流
     */
    ByteArrayOutputStream generateErrorExercisePdf(String courseId);

    /**
     * 生成词汇测试错误内容讲义PDF
     * @param studentWordTestId 单词测试ID
     * @return PDF字节流
     */
    ByteArrayOutputStream generateWordTestErrorHandoutPdf(String studentWordTestId);

    /**
     * 生成词汇测试错误内容练习PDF
     * @param studentWordTestId 单词测试ID
     * @return PDF字节流
     */
    ByteArrayOutputStream generateWordTestErrorExercisePdf(String studentWordTestId);

    /**
     * 生成并上传课程错误内容到OSS
     * @param courseId 课程ID
     * @param type 类型 (error_handout: 错词讲义, error_exercise: 错题练习)
     * @return 下载信息
     */
    CourseMaterialDownloadDto.DownloadInfo generateAndUploadCourseErrorMaterial(String courseId, String type);

    /**
     * 根据条件删除未开始的课程
     * @param teacherId 老师
     * @param studentId 学生
     * @param subject   学科
     * @param specification 课型（单词课、题型课等）
     * @param remark 备注
     */
    void deleteNotStartedCourses(String teacherId, String studentId, String subject, String specification, String remark);

    /**
     * 构建单词信息列表
     * @param textbookItemIds 教材项ID列表
     * @param showDisplayOrder 是否显示排序
     * @return 单词信息列表
     */
    List<org.nonamespace.word.server.dto.course.CourseSectionWordInfo> buildWords(List<String> textbookItemIds, boolean showDisplayOrder);

    /**
     * 生成练习PDF
     * @param sectionDtoList 课程环节列表
     * @param courseId 课程ID
     * @param title1 练习要求1
     * @param title2 练习要求2
     * @param pdfName PDF名称
     * @param showDisplayOrder 是否显示单词原始顺序
     * @return PDF字节流
     */
    ByteArrayOutputStream generatePracticesInfoPdf(List<CourseSectionDto> sectionDtoList, String courseId, String title1, String title2, String pdfName, boolean showDisplayOrder);
}
