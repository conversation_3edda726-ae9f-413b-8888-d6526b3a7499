package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.server.domain.HistoryTextbook;
import org.nonamespace.word.server.domain.Textbook;
import org.nonamespace.word.server.domain.TextbookItem;
import org.nonamespace.word.server.domain.Word;
import org.nonamespace.word.server.dto.course.CourseSectionDto;
import org.nonamespace.word.server.dto.course.CourseSectionWordDto;
import org.nonamespace.word.server.dto.course.CourseSectionWordInfo;
import org.nonamespace.word.server.enums.GradeEnum;
import org.nonamespace.word.server.enums.SemesterEnum;
import org.nonamespace.word.server.enums.TextBookNodeEnum;
import org.nonamespace.word.server.enums.TextBookTypeEnum;
import org.nonamespace.word.server.model.TextbookStructure;
import org.nonamespace.word.server.model.TextbookUnit;
import org.nonamespace.word.server.model.TextbookWord;
import org.nonamespace.word.server.service.ICourseService;
import org.nonamespace.word.server.service.impl.HistoryTextbookServiceImpl;
import org.nonamespace.word.server.service.impl.TextbookItemService;
import org.nonamespace.word.server.service.impl.TextbookService;
import org.nonamespace.word.server.service.impl.WordServiceImpl;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class TextbookManagerServiceImpl implements org.nonamespace.word.server.facade.TextbookManagerService {

    private final WordServiceImpl wordService;
    private final TextbookService textbookService;
    private final TextbookItemService textbookItemService;
    private final HistoryTextbookServiceImpl historyTextbookService;
    private final OssService ossService;
    private final ICourseService courseService;


    public void aiFillWordMeanings(String words) {

    }

    @Override
    public void fixWordMeanings() {
        // 字符串按照分号、中文分号、逗号、中文逗号分割，去重，合并成一个字符串
        Function<String, String> distinct = (x) -> Arrays.stream(x.split("[；;，,\b\n]")).map(String::trim).distinct().collect(Collectors.joining("；"));

        List<Word> words = wordService.lambdaQuery().list();
        words.forEach(x->{
            if(x.getMeanings() != null) {
                x.getMeanings().forEach((k, v) -> {
                    if(v.getPos() != null) {
                        // 把pos.pos一样的对象，def用；拼接一起，合并成同一个
                        List<Word.Meanings.Pos> posList = v.getPos();
                        Map<String, Word.Meanings.Pos> posMap = new HashMap<>();
                        posList.forEach(p -> {
                            if (posMap.containsKey(p.getPos())) {
                                Word.Meanings.Pos exist = posMap.get(p.getPos());
                                exist.setDef(exist.getDef() + p.getDef());
                            } else {
                                posMap.put(p.getPos(), p);
                            }
                        });

                    }
                });
                x.getMeanings().forEach((k, v) -> {
                    if(v.getPos() != null) {
                        v.getPos().forEach(p -> {
                            p.setDef(distinct.apply(p.getDef()));
                        });
                    }
                });
            }
        });
        wordService.updateBatchById(words);
    }

    /**
     * 导入教材
     */
    @Override
    public void importTextbook() {
        List<File> xlsxFiles = getTextbookFiles();
        xlsxFiles.forEach(x-> System.out.println("导入教材: " + x.getAbsolutePath()));

        List<TextbookStructure> textbooks = xlsxFiles.stream()
                .map(this::parseExcelToTextbookJson).toList();

        // 提取全部单词先统一入库
        List<Word> words = new ArrayList<>(textbooks.stream()
                .flatMap(textbook -> textbook.getUnits().stream()
                        .flatMap(unit -> unit.getWords().stream()
                                .map(word -> {
                                    System.out.println(StrUtil.format("{}  {}  {}", textbook.getName(), unit.getName(), word.getWord()));
                                    Word w = new Word();
                                    w.setWord(word.getWord());
                                    w.setSyllables(word.getSyllables());
                                    Word.Meanings meanings = new Word.Meanings();
                                    meanings.setPos(word.getPods());
                                    w.setMeanings(MapUtil.of(textbook.getType().equals(TextBookTypeEnum.SPECIAL.getValue()) ? "特色" : "通用", meanings));
                                    w.setSentences(MapUtil.of(textbook.getType().equals(TextBookTypeEnum.SPECIAL.getValue()) ? "特色" : "通用", word.getSentences()));

                                    w.setFlagPracticeWord(Boolean.FALSE);
                                    w.setFlagPracticeTranslate(Boolean.FALSE);
                                    w.setFlagPracticeOrder(Boolean.FALSE);

                                    w.setDifficulty(1L);
                                    w.setVersion(1L);
                                    w.setDeleted(false);

                                    w.setTags(List.of(textbook.getName()));

                                    return w;
                                })))
                .toList());

        processWords(words);


        // 处理教材
//        textbooks.forEach(this::processTextbook);
    }


    /**
     * 教材入库
     * @param textbook 教材对象
     * @return textbookId 教材id
     */
    @Override
    public  String storeTextbook(Textbook textbook) {
        return storeTextbook(textbook, null);
    }

    @Override
    public void initTextbookBaseInfo(String jsonStr) {
        //一年级到九年级，跟数字的映射表
        // {"一年级":1,"二年级":2,"三年级":3,"四年级":4,"五年级":5,"六年级":6,"七年级":7,"八年级":8,"九年级":9}



        // 学期映射
        // {"上学期":1,"下学期":2}
//        Map<String, Integer> semesterMap = new HashMap<>();
//        semesterMap.put("上学期", 1);
//        semesterMap.put("下学期", 2);
//        semesterMap.put("全年", 3);


        JSONObject json = JSONUtil.parseObj(jsonStr);
        List<Textbook> textbooks = textbookService.lambdaQuery().in(Textbook::getName, json.keySet()).list();
        textbooks.forEach(x->{
            JSONObject obj = json.getJSONObject(x.getName());
            x.setPublisher(obj.getStr("版本", ""));
            x.setGrade(GradeEnum.fromText(obj.getStr("年级", "")).getValue());
            x.setSemester(SemesterEnum.fromText(obj.getStr("学期", "")).getValue());
            x.setRequired(obj.getStr("必修", ""));
            textbookService.updateById(x);
        });

    }


    /**
         * 教材入库
         * @param textbook
         * @return textbookId 教材id
         */
    private String storeTextbook(Textbook textbook, List<Word> words) {

        if(StrUtil.isEmpty(textbook.getStage())){
            textbook.setStage(GradeEnum.toStage(textbook.getGrade()));
        }

        processWords(words);

        String textbookId = textbook.getId();

        Textbook existingTextbook = null;


        // 判断是否存在同名教材
        String finalTextbookId = textbookId;
        List<Textbook> textbooks = textbookService.lambdaQuery().or(x->x.eq(Textbook::getId, finalTextbookId)).or(x->x.eq(Textbook::getName, textbook.getName())).list();

        if(CollUtil.isNotEmpty(textbooks)) {
            Optional<Textbook> sameName = textbooks.stream().filter(x -> x.getName().equals(textbook.getName().trim())).findFirst();
            if(sameName.isPresent()) {
                // 如果教材名称相同但ID不同，则不允许更新教材
                if(!StrUtil.equals(textbookId, sameName.get().getId())) {
                    throw new RuntimeException("教材名称已存在，请修改教材名称");
                }else{
                    existingTextbook = sameName.get();
                }
            }

            if(StrUtil.isNotEmpty(textbook.getId())){
                Optional<Textbook> sameId = textbooks.stream().filter(x -> x.getId().equals(textbook.getId().trim())).findFirst();
                if(sameId.isPresent()) {
                    existingTextbook = sameId.get();
                } else {
                    throw new RuntimeException("教材不存在，请检查ID是否正确");
                }
            }

        }


        if(existingTextbook != null) {
            // 更新教材
            updateExistingTextbook(existingTextbook, textbook, words);
        } else {
            // 新建教材
            textbook.setId(null);
            createNewTextbook(textbook, words);
            textbookId = textbook.getId();
        }

        return textbookId;

    }

    /**
     * 更新教材
     * @param existingTextbook
     * @param textbook
     * @param words
     */
    private void updateExistingTextbook(Textbook existingTextbook, Textbook textbook, List<Word> words) {

        // region 备份旧数据
        HistoryTextbook historyTextbook = new HistoryTextbook();
        historyTextbook.setId(IdUtil.getSnowflakeNextIdStr());
        historyTextbook.setTextbookId(existingTextbook.getId());
        historyTextbook.setTextbookObj(JSONUtil.toJsonStr(existingTextbook));
        historyTextbook.setTextbookItemsObj(JSONUtil.toJsonStr(textbookItemService.lambdaQuery().eq(TextbookItem::getTextbookId, existingTextbook.getId()).list()));
        historyTextbook.setChangeDescription("更新教材");
        historyTextbook.setVersion(existingTextbook.getVersion());

        historyTextbookService.save(historyTextbook);

        // endregion

        // 判断新旧wordlist是否一致，一致则不需要更新textbookitem
        if(!StrUtil.equals(trimWordlist(existingTextbook.getWordList()), trimWordlist(textbook.getWordList()))) {
            processTextbookItemDiff(existingTextbook, textbook);
//            textbookItemService.compareTextbookItem(textbook.getId(), textbook.getWordList());
        }


        // region textbook更新字段复制

        if(StrUtil.isNotEmpty(textbook.getName())){
            existingTextbook.setName(textbook.getName());
        }
        if(StrUtil.isNotEmpty(textbook.getDescription())){
            existingTextbook.setDescription(textbook.getDescription());
        }
        if(StrUtil.isNotEmpty(textbook.getCover())){
            existingTextbook.setCover(textbook.getCover());
        }
        if(StrUtil.isNotEmpty(textbook.getType())){
            existingTextbook.setType(textbook.getType());
        }

        if(textbook.getSemester() != null){
            existingTextbook.setSemester(textbook.getSemester());
        } else {
            // 如果没有设置学期，则默认设置为全年
            existingTextbook.setSemester(3);
        }

        if(StrUtil.isNotEmpty(textbook.getWordList())) {
            existingTextbook.setWordList(textbook.getWordList());
        }

        if(StrUtil.isNotEmpty(textbook.getPublisher())){
            existingTextbook.setPublisher(textbook.getPublisher());
        }
        if(StrUtil.isNotEmpty(textbook.getRequired())){
            existingTextbook.setRequired(textbook.getRequired());
        }

        existingTextbook.setGrade(textbook.getGrade());

        existingTextbook.setStage(getStage(textbook.getGrade()));

        // endregion

        textbookService.updateById(existingTextbook);
    }

    /**
     * 处理textbook_item差异更新
     * @param existingTextbook
     * @param textbook
     */
    private void processTextbookItemDiff(Textbook existingTextbook, Textbook textbook) {

        Map<String, List<TextbookItem>> unitIdMap = new HashMap<>();
        List<TextbookItem> textbookItems = convertWordlistToTextbookItemList(textbook.getWordList(), unitIdMap);
        List<TextbookItem> existingTextbookItems = textbookItemService.lambdaQuery().eq(TextbookItem::getTextbookId, existingTextbook.getId()).list();

        // 更新单元数
        existingTextbook.setStatUnitCnt(textbookItems.stream().filter(x->x.getNodeType() == TextBookNodeEnum.UNIT.getValue()).count());
        // 更新单词数
        existingTextbook.setStatWordCnt(textbookItems.stream().filter(x->x.getNodeType() == TextBookNodeEnum.WORD.getValue()).count());

        Map<String, TextbookItem> existUnitMap = existingTextbookItems.stream().filter(x -> x.getNodeType() == TextBookNodeEnum.UNIT.getValue()).collect(Collectors.toMap(TextbookItem::getId, Function.identity()));

        // 构建Map时需要区分单元和单词，避免key冲突
        Map<String, TextbookItem> newTextbookItemMap = textbookItems.stream().collect(Collectors.toMap(x -> {
            if (x.getNodeType() == TextBookNodeEnum.UNIT.getValue()) {
                return "UNIT_" + x.getUnitName();
            } else {
                return "WORD_" + x.getUnitName() + "_" + x.getWordId();
            }
        }, x -> x));
        TextbookItem defaultUnitItem = new TextbookItem().setUnitName("null");
        Map<String, TextbookItem> existingTextbookItemMap = existingTextbookItems.stream()
                .peek(x-> {
                    if(x.getNodeType() != TextBookNodeEnum.UNIT.getValue() && !Objects.equals(x.getPid(), "0"))
                        x.setUnitName(existUnitMap.get(x.getPid()).getUnitName());
                })
                .collect(Collectors.toMap(x -> {
            if (x.getNodeType() == TextBookNodeEnum.UNIT.getValue()) {
                return "UNIT_" + x.getUnitName();
            } else {
                return "WORD_" + existUnitMap.getOrDefault(x.getPid(), defaultUnitItem).getUnitName() + "_" + x.getWordId();
            }
        }, x -> x));

        // region 相同项，保持原有的id，准备更新操作
        List<TextbookItem> toUpdate = new ArrayList<>();
        {
            // 取newTextbookItemMap和existingTextbookItemMap这两个key的交集
            Set<String> commonKeys = new HashSet<>(newTextbookItemMap.keySet());
            commonKeys.retainAll(existingTextbookItemMap.keySet());

            // 把新的textBookItem.id更新为原有的，并准备更新操作
            commonKeys.forEach(key -> {
                TextbookItem newItem = newTextbookItemMap.get(key);
                TextbookItem existingItem = existingTextbookItemMap.get(key);
                if(newItem.getNodeType() == TextBookNodeEnum.UNIT.getValue()) {
                    // 对于单元，需要更新unitIdMap中的映射关系
                    List<TextbookItem> wordsInNewUnit = unitIdMap.get(newItem.getId());
                    if (wordsInNewUnit != null) {
                        unitIdMap.put(existingItem.getId(), wordsInNewUnit);
                        unitIdMap.remove(newItem.getId());
                    }
                    newItem.setId(existingItem.getId());
                } else if(newItem.getNodeType() == TextBookNodeEnum.WORD.getValue()) {
                    newItem.setId(existingItem.getId());
                }
                // 添加到更新列表
                toUpdate.add(newItem);
            });

            // 从textbookItems中移除这些需要更新的项目，避免重复插入
            textbookItems.removeIf(item -> {
                String itemKey;
                if (item.getNodeType() == TextBookNodeEnum.UNIT.getValue()) {
                    itemKey = "UNIT_" + item.getUnitName();
                } else {
                    itemKey = "WORD_" + item.getUnitName() + "_" + item.getWordId();
                }
                return commonKeys.contains(itemKey);
            });

        }
        // endregion

        // region 删除项，逻辑删除原有的
        {
            // 取newTextbookItemMap.keySet没有但是existingTextbookItemMap.keySet有的
            Set<String> deletedKeys = new HashSet<>(existingTextbookItemMap.keySet());
            deletedKeys.removeAll(newTextbookItemMap.keySet());

            List<String> delIds = existingTextbookItemMap.keySet().stream().filter(deletedKeys::contains).map(x -> existingTextbookItemMap.get(x).getId()).toList();
            textbookItemService.removeByIds(delIds);

        }
        // endregion

        // 强制更新textbookId
        textbookItems.forEach(x-> x.setTextbookId(existingTextbook.getId()));
        toUpdate.forEach(x-> x.setTextbookId(existingTextbook.getId()));

        unitIdMap.forEach((k,v)->{
            v.forEach(x->x.setPid(k));
        });

        // 分别处理插入和更新操作
        if (!textbookItems.isEmpty()) {
            textbookItemService.saveBatch(textbookItems);
        }
        if (!toUpdate.isEmpty()) {
            textbookItemService.updateBatchById(toUpdate);
        }

    }

    /**
     * 创建新教材
     * @param textbook
     * @param words
     */
    private void createNewTextbook(Textbook textbook, List<Word> words) {


        // 保存教材项
        Map<String, List<TextbookItem>> unitIdMap = new HashMap<>();
        List<TextbookItem> textbookItems = convertWordlistToTextbookItemList(textbook.getWordList(), unitIdMap);
        textbookItems.forEach(x-> x.setTextbookId(textbook.getId()));

        // 单元数
        textbook.setStatUnitCnt(textbookItems.stream().filter(x->x.getNodeType() == TextBookNodeEnum.UNIT.getValue()).count());
        // 单词数
        textbook.setStatWordCnt(textbookItems.stream().filter(x->x.getNodeType() == TextBookNodeEnum.WORD.getValue()).count());


        textbookService.save(textbook);

        textbookItems.forEach(x-> x.setTextbookId(textbook.getId()));
        textbookItemService.saveBatch(textbookItems);

    }


    /**
     * 把wordlist转成textbook_item，如果存在新单词，会入库
     * @param wordlist
     * @return
     */
    private List<TextbookItem> convertWordlistToTextbookItemList(String wordlist, Map<String, List<TextbookItem>> unitIdMap) {

        List<TextbookItem> textbookItems = new ArrayList<>();
        List<String> lines = Arrays.stream(wordlist.split("\\r?\\n")).map(String::trim).toList();

        List<String> wordTexts = lines.stream().filter(x -> !x.trim().startsWith(">")).toList();

        Map<String, String> wordIdMap = new HashMap<>();

        // region 处理新增单词
        if(CollUtil.isNotEmpty(wordTexts)) {

            List<Word> existWords = wordService.lambdaQuery().select(Word::getId, Word::getWord).in(Word::getWord, wordTexts).list();
            wordIdMap.putAll(existWords.stream().collect(Collectors.toMap(Word::getWord, Word::getId, (x, y) -> x)));

            // 找出不存在的单词入库
            List<Word> newWords = wordTexts.stream().filter(x -> !wordIdMap.containsKey(x)).map(x -> {
                Word word = new Word();
                word.setWord(x);
                word.setDifficulty(1L);
                word.setVersion(1L);
                word.setDeleted(false);
                word.setFlagPracticeWord(false);
                word.setFlagPracticeTranslate(false);
                word.setFlagPracticeOrder(false);
                return word;
            }).toList();

            wordService.saveBatch(newWords);

            wordIdMap.putAll(newWords.stream().collect(Collectors.toMap(Word::getWord, Word::getId, (x, y) -> x)));
        }
        // endregion

        // region 处理textbook_item
        String currentUnitName = null;
        String unitId = "0";
        int unitOrder = 1;
        int wordOrder = 1;
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            if (line.startsWith(">")) {
                currentUnitName = line.substring(1).trim();
                unitId = IdUtil.getSnowflakeNextIdStr();
                TextbookItem unitItem = new TextbookItem();
                unitItem.setId(unitId);
                unitItem.setNodeType(TextBookNodeEnum.UNIT.getValue());
                unitItem.setUnitName(currentUnitName);
                unitItem.setPid("0");
                unitItem.setDisplayOrder(unitOrder++);
                textbookItems.add(unitItem);
                unitIdMap.computeIfAbsent(unitId, k -> new ArrayList<>());
//                 wordOrder = 1;
            } else {
                TextbookItem wordItem = new TextbookItem();
                wordItem.setId(IdUtil.getSnowflakeNextIdStr());
                wordItem.setNodeType(TextBookNodeEnum.WORD.getValue());
                wordItem.setPid(unitId);
                wordItem.setWordId(wordIdMap.get(line));
                wordItem.setDisplayOrder(wordOrder++);
                textbookItems.add(wordItem);
                wordItem.setUnitName(currentUnitName);
                unitIdMap.computeIfAbsent(unitId, k -> new ArrayList<>()).add(wordItem);
            }
        }
        // endregion

        return textbookItems;

    }

    /**
     * 处理单词，仅对新增单词入库，不对已存在的单词做处理
     * @param words
     */
    private void processWords(List<Word> words){

        if(CollUtil.isEmpty(words)) {
            return;
        }

        // region 单词去重合并处理

        // 遍历words如果有重复的word.word
        // 1. 保留首次出现的word
        // 2. 合并其他的tags到首次出现的word
        // 3. 合并其他的meanings到首次出现的meanings
        // 3.1 取meanings.pos列表, 如果pod.pod相同，则合并pod.def，不同最佳到meanings.pod数组中
        // 对words去重，合并tags和meanings
        Map<String, Word> wordMap = new LinkedHashMap<>();
        for (Word word : words) {
            String key = word.getWord();
            if (!wordMap.containsKey(key)) {
                wordMap.put(key, word);
            } else {
                Word first = wordMap.get(key);
                mergeWord(first, word);
            }
        }
        words.clear();
        words.addAll(wordMap.values());

        // endregion

        List<Word> existWords = wordService.lambdaQuery().select(Word::getId, Word::getWord, Word::getSentences).in(Word::getWord, wordMap.keySet()).list();

//        if(CollUtil.isNotEmpty(existWords)) {
//            Map<String, Word> existWordMap = existWords.stream().collect(Collectors.toMap(Word::getWord, x -> x, (x, y) -> x));
//            existWordMap.forEach((key, value) -> mergeWord(value, wordMap.get(key)));
//            wordMap.entrySet().removeIf(e-> existWordMap.containsKey(e.getKey()));
//            wordService.updateBatchById(existWordMap.values());
//        }


//        // 已存在的单词，忽略
//        if(CollUtil.isNotEmpty(existWords)) {
//            Map<String, Word> existWordMap = existWords.stream().collect(Collectors.toMap(Word::getWord, x -> x, (x, y) -> x));
//            wordMap.entrySet().removeIf(e-> existWordMap.containsKey(e.getKey()));
//        }
//
        // 刷新特色词库例句

        String[] fixWordArr = {"wipe","zest","firstly"};

        List<String> fixWords = Arrays.stream(fixWordArr).toList();

        if(CollUtil.isNotEmpty(existWords)) {
             Map<String, Word> existWordMap = existWords.stream().collect(Collectors.toMap(Word::getWord, x -> x, (x, y) -> x));
             existWordMap.forEach((k, v) -> {
                 if(!fixWords.contains(k)){
                     return;
                 }

                 Word word = wordMap.get(k);
                 List<Word.Sentences> existSentences = v.getSentences().get("特色");
                 List<Word.Sentences> newSentences = word.getSentences().get("特色");
                 newSentences.forEach(sentence -> {
                     sentence.setSentenceEn(sentence.getSentenceEn().replaceAll("\\u00A0", " "));
                     if(StrUtil.isNotEmpty(sentence.getSyllables())){
                         sentence.setSyllables(sentence.getSyllables().replaceAll("\\u00A0", " "));
                     }

                 });
                 if(existSentences == null){
                     System.out.println("新增特色词库例句: " + v.getWord());
                     v.getSentences().put("特色", newSentences);
                 }else {
                     existSentences.forEach((sentence) -> {
                         sentence.setSentenceEn(sentence.getSentenceEn().replaceAll("\\u00A0", " "));
                         if (StrUtil.isNotEmpty(sentence.getSyllables())) {
                             sentence.setSyllables(sentence.getSyllables().replaceAll("\\u00A0", " "));
                         }
                     });
//                     existSentences.removeIf(x->StrUtil.isEmpty(x.getStage()));

                     newSentences.forEach(n -> {

                         if (existSentences.stream().anyMatch(x -> Objects.equals(x.getSentenceEn(), n.getSentenceEn()))) {
                             return;
                         }

                         existSentences.add(n);

//                         List<Word.Sentences> list = existSentences.stream().filter(x -> x.getSentenceEn().equals(n.getSentenceEn())).collect(Collectors.toList());

//                         if (list.isEmpty()) {
//                             existSentences.add(n);
//                             return;
//                         }
//
//                         existSentences.removeAll(list);
//
//                         Word.Sentences first = list.getFirst();
//
//                         if (StrUtil.isNotEmpty(n.getSyllables())) {
//                             list.removeIf(x -> !Objects.equals(x.getSyllables(), n.getSyllables()));
//                         }
//
//                         for (Word.Sentences sentence : list) {
//                             if (StrUtil.isNotEmpty(sentence.getStage())) {
//                                 continue;
//                             }
//
//                             sentence.setStage(n.getStage());
//                         }
//
//                         if (list.isEmpty()) {
//                             first.setStage(n.getStage());
//                             first.setSyllables(n.getSyllables());
//                             list.add(first);
//                         }
//
//                         existSentences.addAll(list);


                     });
                 }

                 System.out.println("更新特色词库例句: " + v.getWord() + "  " + v.getSentences().get("特色").size() + "条");
                 wordService.lambdaUpdate()
                         .set(Word::getSentences, v.getSentences(), "typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler")
                         .eq(Word::getId, v.getId()).update();
             });
        }


        List<Word> addWords = new ArrayList<>();
        for (String s : fixWordArr) {
            addWords.add(wordMap.get(s));
        }


        wordService.saveBatch(addWords);

//        wordService.saveBatch(wordMap.values());

        if(true)
            return;


        // 新增单词入库
        wordMap.values().forEach(x-> {
            x.setDifficulty(1L);
            x.setFlagPracticeOrder(Boolean.FALSE);
            x.setFlagPracticeWord(Boolean.FALSE);
            x.setFlagPracticeTranslate(Boolean.FALSE);
        });
    }


    /**
     * 合并两个单词到第一个单词中
     * @param one
     * @param tow
     */
    private void mergeWord(Word one, Word tow) {
        // 合并tags
        Set<String> tags = new LinkedHashSet<>();
        if (one.getTags() != null) tags.addAll(one.getTags());
        if (tow.getTags() != null) tags.addAll(tow.getTags());
        one.setTags(new ArrayList<>(tags));
        // 合并meanings
        if (one.getMeanings() == null) {
            one.setMeanings(tow.getMeanings());
        } else {
            if (tow.getMeanings() != null) {
                tow.getMeanings().keySet().forEach(x->{
                    if(!one.getMeanings().containsKey(x)){
                        one.getMeanings().put(x, tow.getMeanings().get(x));
                    }else {
                        // 合并pos
                        List<Word.Meanings.Pos> posList = one.getMeanings().get(x).getPos();
                        List<Word.Meanings.Pos> newPosList = tow.getMeanings().get(x).getPos();
                        if (newPosList != null) {
                            for (Word.Meanings.Pos pos : newPosList) {
                                Optional<Word.Meanings.Pos> exist = posList.stream()
                                        .filter(p -> Objects.equals(p.getPos(), pos.getPos()))
                                        .findFirst();
                                if (exist.isPresent()) {
                                    // 合并def
                                    String def = exist.get().getDef();
                                    if (!def.contains(pos.getDef())) {
                                        exist.get().setDef(Arrays.stream(ArrayUtil.addAll(exist.get().getDef().split("[；;，,]"), pos.getDef().split("[；;，,]"))).distinct().collect(Collectors.joining("；")));
                                    }
                                } else {
                                    posList.add(pos);
                                }
                            }
                        }
                    }
                });
            }
        }

        // 合并sentences
        if (one.getSentences() == null) {
            one.setSentences(tow.getSentences());
        } else {
            if (tow.getSentences() != null) {
                tow.getSentences().keySet().forEach(x->{
                    if(!one.getSentences().containsKey(x)){
                        one.getSentences().put(x, tow.getSentences().get(x));
                    }else {
                        List<Word.Sentences> sentences = one.getSentences().get(x);
                        List<Word.Sentences> newSentences = tow.getSentences().get(x);
                        if (newSentences != null) {
                            for (Word.Sentences sentence : newSentences) {
                                Optional<Word.Sentences> exist = sentences.stream()
                                        .filter(s -> Objects.equals(s.getSentenceEn(), sentence.getSentenceEn()))
                                        .findFirst();
                                if (exist.isEmpty()) {
                                    sentences.add(sentence);
                                }
                            }
                        }
                    }
                });
            }
        }

    }

    /**
     * 对wordlist去空格处理
     * @param wordlist
     * @return
     */
    private String trimWordlist(String wordlist){
        if(wordlist == null){
            return wordlist;
        }
        return Arrays.stream(wordlist.split("\\r?\\n"))
                .map(x-> {
                    x = x.trim();
                    if(x.startsWith(">")){
                        return "> " + x.substring(1).trim();
                    }else{
                        return x;
                    }
                })
                .filter(x -> !x.isEmpty())
                .collect(Collectors.joining("\n"));
    }


    private void processTextbook(TextbookStructure textbookStructure) {
        Textbook textbook = new Textbook();
        textbook.setName(textbookStructure.getName());
        textbook.setWordList(textbookStructure.getText());
        textbook.setType(textbookStructure.getType());

        storeTextbook(textbook, null);
    }


    private List<File> getTextbookFiles() {
        // 递归指定路径下的所有xlsx文件，含括子目录
        File textbookDir;
        try {
            textbookDir = Paths.get(ResourceUtil.getResource("textbook").toURI()).toFile();
        } catch (URISyntaxException e) {
            throw new RuntimeException("教材目录不存在");
        }
        if(!textbookDir.exists()) {
            throw new RuntimeException("教材目录不存在: " + textbookDir.getAbsolutePath());
        }
        return FileUtil.loopFiles(textbookDir, file -> file.getName().toLowerCase().endsWith(".xlsx") && !file.getName().startsWith("."));
    }

    private TextbookStructure parseExcelToTextbookJson(File file) {
        String fileNameWithoutExt = FileUtil.mainName(file);

        TextbookStructure result = new TextbookStructure();
        result.setName(fileNameWithoutExt);

        System.out.println("Parsing file: " + file.getAbsolutePath());
        List<String> sheetNames = ExcelUtil.getReader(file).getSheetNames();
        StringBuilder wordListText = new StringBuilder();

        boolean generateUnit = sheetNames.size()>1;

        if(file.getName().startsWith("核心315词库") || file.getName().startsWith("核心350词库")) {

            result.setType(TextBookTypeEnum.SPECIAL.getValue());

            // 核心词库解析

            ExcelReader reader = ExcelUtil.getReader(file);

            List<TextbookWord> textbookWords = new ArrayList<>();

            // 从第二行开始读取数据（跳过表头）
            for (int i = 1; i < reader.getRowCount(); i += 3) {
                if (i + 1 < reader.getRowCount()) {
                    TextbookWord textbookWord = new TextbookWord();
                    List<Object> row1 = reader.readRow(i);
                    List<Object> row2 = reader.readRow(i + 1);
                    List<Object> row3 = reader.readRow(i + 2);

                    textbookWord.setWord(getCell(row1, 1));
                    textbookWord.setPhonetic(getCell(row2, 1));
                    String cell = getCell(row3, 1);
                    textbookWord.getPods().add(new Word.Meanings.Pos(cell.contains(".") ? cell.substring(0, cell.indexOf(".")) : "", cell.contains(".") ? cell.substring(cell.indexOf(".") + 1) : cell));

                    // 处理核心句型 1
                    Word.Sentences sentence1 = new Word.Sentences();
                    sentence1.setSentenceEn(getCell(row1, 2));
                    sentence1.setSyllables(getCell(row2, 2));
                    sentence1.setSentenceCn(getCell(row3, 2));
                    sentence1.setStage(GradeEnum.toStage(GradeEnum.GRADE_1));
                    textbookWord.getSentences().add(sentence1);

                    // 处理核心句型 2
                    String sentence2En = getCell(row1, 3);
                    if(StrUtil.isNotEmpty(sentence2En)) {
                        Word.Sentences sentence2 = new Word.Sentences();
                        sentence2.setSentenceEn(sentence2En);
                        sentence2.setSyllables(getCell(row2, 3));
                        sentence2.setSentenceCn(getCell(row3, 3));
                        sentence2.setStage(GradeEnum.toStage(GradeEnum.GRADE_1));
                        textbookWord.getSentences().add(sentence2);
                    }

                    textbookWords.add(textbookWord);
                }
            }


            TextbookUnit unit = new TextbookUnit();
            result.getUnits().add(unit);
            unit.setName("");

            textbookWords.forEach(x-> {
                unit.getWords().add(x);
                wordListText.append(x.getWord()).append("\r\n");
            });

            result.setText(wordListText.toString());

            // 关闭 Excel 读取器
            reader.close();
        }else {

            boolean isNormal = !(file.getName().startsWith("核心高频1278词库") || file.getName().startsWith("核心拓展2222词库"));

            String stage = file.getName().startsWith("核心高频1278词库") ? "初中" : (file.getName().startsWith("核心拓展2222词库") ? "高中" : "");

            result.setType(isNormal ? TextBookTypeEnum.SCHOOL.getValue() : TextBookTypeEnum.SPECIAL.getValue());

            sheetNames.forEach(sheetName -> {
                ExcelReader sheetReader = ExcelUtil.getReader(file, sheetName);
                List<List<Object>> rawRows = sheetReader.read();

                if (rawRows == null || rawRows.size() < 2) return;

                if (generateUnit) {
                    wordListText.append("> ").append(sheetName).append("\r\n");
                }

                List<List<Object>> dataRows = rawRows.subList(1, rawRows.size());

                TextbookUnit unit = new TextbookUnit();
                unit.setName(sheetName);

                dataRows.stream()
                        .filter(row -> !row.isEmpty() && !StrUtil.isBlankIfStr(row.getFirst()))
                        .forEach(row -> {
                            TextbookWord wordObj = isNormal ? createNormalWordObjectFromRow(row, stage) : createSpecialWordObjectFromRow(row, stage);
                            unit.getWords().add(wordObj);
                            wordListText.append(wordObj.getWord()).append("\r\n");
                        });

                result.getUnits().add(unit);

                sheetReader.close();
            });
        }

        result.setText(wordListText.toString());


        return result;
    }

    private TextbookWord createNormalWordObjectFromRow(List<Object> row, String stage) {
        TextbookWord wordObj = new TextbookWord();
        wordObj.setWord(getCell(row, 0));
        wordObj.setPhonetic(getCell(row, 2));
        wordObj.setSyllables(getCell(row, 3));
        List<Word.Sentences> sentences = new ArrayList<>();
        Word.Sentences sentence = new Word.Sentences();

        sentence.setSentenceEn(getCell(row, 4));
        sentence.setSentenceCn(getCell(row, 5));
        sentence.setStage(stage);

        sentences.add(sentence);

        wordObj.getSentences().addAll(sentences);

        String cell = getCell(row, 1);
        Word.Meanings.Pos meaning = new Word.Meanings.Pos();
        meaning.setPos(cell.contains(".") ? cell.substring(0, cell.indexOf(".")) : "");
        meaning.setDef(cell.contains(".") ? cell.substring(cell.indexOf(".") + 1) : cell);
        wordObj.getPods().add(meaning);

        return wordObj;
    }

    private TextbookWord createSpecialWordObjectFromRow(List<Object> row, String stage) {
        TextbookWord wordObj = new TextbookWord();
        wordObj.setWord(getCell(row, 1));
        wordObj.setPhonetic(getCell(row, 2));
        List<Word.Sentences> sentences = new ArrayList<>();
        Word.Sentences sentence = new Word.Sentences();

        sentence.setSentenceEn(getCell(row, 4));
        sentence.setSyllables(getCell(row, 5));
        sentence.setSentenceCn(getCell(row, 6));
        sentence.setStage(stage);
        sentences.add(sentence);
        wordObj.getSentences().addAll(sentences);

        String cell = getCell(row, 3);
        Word.Meanings.Pos meaning = new Word.Meanings.Pos();
        meaning.setPos(cell.contains(".") ? cell.substring(0, cell.indexOf(".")) : "");
        meaning.setDef(cell.contains(".") ? cell.substring(cell.indexOf(".") + 1) : cell);
        wordObj.getPods().add(meaning);

        return wordObj;
    }

    private static String getCell(List<Object> row, int index) {
        if (index < row.size()) {
            Object val = row.get(index);
            return val == null ? "" : val.toString().trim();
        }
        return "";
    }

    private String getStage(Integer grade){
        if(grade == null) return null;
        if(grade <=6) return "小学";
        if(grade <=9) return "初中";
        if(grade <=12) return "高中";
        return null;
    }
    public void fixSentences() {
        // 处理教材中的例句，去掉多余的空格和换行符

        List<Word> words = wordService.lambdaQuery().like(Word::getTags, "核心").list();
        words.forEach(x->{
            if(x.getSentences() != null) {
                x.getSentences().forEach((k, v) -> {
                    if(v != null) {
                        v.forEach(s -> {
                            s.setSentenceEn(StrUtil.cleanBlank(s.getSentenceEn()));
                            s.setSentenceCn(StrUtil.cleanBlank(s.getSentenceCn()));
                        });
                    }
                });
            }
        });
        wordService.updateBatchById(words);
    }

    public byte[] generateAudioZip(List<String> textbookItemIds, Textbook textbook) {


        // 创建临时目录用于存储音频文件
        Path tempDir = null;
        Path zipFile = null;

        try {
            tempDir = Files.createTempDirectory("audio_download_" + textbook.getId() + "_" + IdUtil.getSnowflakeNextIdStr() + "_");
            Path wordAudioDir = tempDir.resolve("单词音频");
            Path sentenceAudioDir = tempDir.resolve("例句音频");
            Files.createDirectories(wordAudioDir);
            Files.createDirectories(sentenceAudioDir);

            // 获取单词数据
            List<JSONObject> words = getWordsFromTextbookItems(textbookItemIds, textbook);

            log.info("开始下载音频文件到临时目录: {}, 共{}个单词", tempDir, words.size());

            words.parallelStream().forEach(obj->{
                Word word = obj.getBean("word", Word.class);
                int index = obj.getInt("displayOrder");
                // 下载单词音频到临时目录
                try {
                    downloadWordAudioToDir(word, index, wordAudioDir);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                // 下载例句音频到临时目录
                try {
                    downloadSentenceAudioToDir(word, index, sentenceAudioDir);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });

            log.info("音频文件下载完成，开始压缩ZIP文件");

            // 创建ZIP文件
            zipFile = tempDir.resolve("audio.zip");
            createZipFromDirectory(tempDir, zipFile);

            // 读取ZIP文件内容
            byte[] zipBytes = Files.readAllBytes(zipFile);

            log.info("ZIP文件生成完成，大小: {} MB", zipBytes.length / 1024.0 / 1024.0);

            return zipBytes;

        } catch (Exception e) {
            log.error("生成音频ZIP文件失败", e);
            throw new RuntimeException("生成音频ZIP文件失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempDir != null) {
                try {
                    deleteDirectoryRecursively(tempDir);
                    log.info("临时目录清理完成: {}", tempDir);
                } catch (IOException e) {
                    log.warn("清理临时目录失败: {}", tempDir, e);
                }
            }
        }
    }

    /**
     * 从教材项ID列表获取单词数据
     */
    private List<JSONObject> getWordsFromTextbookItems(List<String> textbookItemIds, Textbook textbook) {
        if (CollUtil.isEmpty(textbookItemIds)) {
            return new ArrayList<>();
        }

        // 获取单词ID列表
        List<TextbookItem> textbookItems = textbookItemService.lambdaQuery()
                .in(TextbookItem::getId, textbookItemIds)
                .eq(TextbookItem::getNodeType, TextBookNodeEnum.WORD.getValue())
                .orderByAsc(TextbookItem::getDisplayOrder)
                .list();
        List<String> wordIds = textbookItems
                .stream()
                .map(TextbookItem::getWordId)
                .filter(Objects::nonNull)
                .toList();

        if (CollUtil.isEmpty(wordIds)) {
            return new ArrayList<>();
        }

        // 获取单词详细信息
        List<Word> words = wordService.lambdaQuery()
                .in(Word::getId, wordIds)
                .list();

        String type = textbook.getType().equals("2") ? "特色" : "通用";
        String stage = textbook.getStage();

        // 过滤出指定类型和阶段的例句
        words.forEach(word -> {
            if (word.getSentences() != null) {
                List<Word.Sentences> sentences = word.getSentences().get(type);
                if(sentences == null) {
                    sentences = new ArrayList<>();
                }
                word.getSentences().clear();
                word.getSentences().put(type, sentences);
                word.getSentences().forEach((k, v) -> {
                    if (CollUtil.isNotEmpty(v)) {
                        v.removeIf(sentence -> !Objects.equals(sentence.getStage(), stage));
                    }
                });
            }
        });

        Map<String, Word> wordMap = words.stream().collect(Collectors.toMap(Word::getId, Function.identity(), (x, y) -> x));
        List<JSONObject> objects = new ArrayList<>();
        textbookItems.forEach(item -> {
            if (item.getWordId() != null) {
                Word word = wordMap.get(item.getWordId());
                if (word != null) {
                    objects.add(new JSONObject().putOpt("word", word).putOpt("displayOrder", item.getDisplayOrder()));
                }
            }
        });

        return objects;
    }



    /**
     * 清理文件名中的特殊字符
     */
    private String sanitizeFileName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "unknown";
        }

        // 替换特殊字符为下划线，限制长度
        String sanitized = fileName.replaceAll("[\\\\/:*?\"<>|\\s]", "_")
                .replaceAll("_+", "_")
                .trim();

        // 限制文件名长度
        if (sanitized.length() > 50) {
            sanitized = sanitized.substring(0, 50);
        }

        return sanitized;
    }

    /**
     * 下载单词音频到指定目录
     */
    private void downloadWordAudioToDir(Word word, int index, Path audioDir) throws IOException {
        String wordText = word.getWord();
        String safeWordText = sanitizeFileName(wordText);

        // 下载美式发音
        if (StrUtil.isNotBlank(word.getAudioUsUrl())) {
            String fileName = String.format("%d.%s_US.mp3", index, safeWordText);
            downloadAudioFileToDir(word.getAudioUsUrl(), audioDir.resolve(fileName));
        }

        // 下载英式发音
        if (StrUtil.isNotBlank(word.getAudioUkUrl())) {
            String fileName = String.format("%d.%s_UK.mp3", index, safeWordText);
            downloadAudioFileToDir(word.getAudioUkUrl(), audioDir.resolve(fileName));
        }
    }

    /**
     * 下载例句音频到指定目录
     */
    private void downloadSentenceAudioToDir(Word word, int index, Path audioDir) throws IOException {
        if (word.getSentences() == null) {
            return;
        }

        String wordText = word.getWord();
        String safeWordText = sanitizeFileName(wordText);

        for (Map.Entry<String, List<Word.Sentences>> entry : word.getSentences().entrySet()) {
            List<Word.Sentences> sentences = entry.getValue();
            if (CollUtil.isNotEmpty(sentences)) {
                for (Word.Sentences sentence : sentences) {
                    String sentenceText = sentence.getSentenceEn();
                    if (StrUtil.isNotBlank(sentenceText)) {
                        String safeSentenceText = sanitizeFileName(sentenceText);

                        // 下载美式发音
                        if (StrUtil.isNotBlank(sentence.getAudioUsUrl())) {
                            String fileName = String.format("%d.%s_%s_US.mp3",
                                    index, safeWordText, safeSentenceText);
                            downloadAudioFileToDir(sentence.getAudioUsUrl(), audioDir.resolve(fileName));
                        }

                        // 下载英式发音
                        if (StrUtil.isNotBlank(sentence.getAudioUkUrl())) {
                            String fileName = String.format("%d.%s_%s_UK.mp3",
                                    index, safeWordText, safeSentenceText);
                            downloadAudioFileToDir(sentence.getAudioUkUrl(), audioDir.resolve(fileName));
                        }
                    }
                }
            }
        }
    }

    /**
     * 下载音频文件到指定路径
     */
    private void downloadAudioFileToDir(String audioUrl, Path filePath) throws IOException {
        if (StrUtil.isBlank(audioUrl)) {
            return;
        }

        try {
            URL url = new URL(audioUrl);
            try (InputStream inputStream = url.openStream()) {
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
                log.debug("音频文件下载成功: {}", filePath);
            }
        } catch (Exception e) {
            log.warn("下载音频文件失败: {} -> {}, 错误: {}", audioUrl, filePath, e.getMessage());
            // 不抛出异常，继续下载其他文件
        }
    }

    /**
     * 从目录创建ZIP文件
     */
    private void createZipFromDirectory(Path sourceDir, Path zipFile) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(zipFile))) {
            Files.walk(sourceDir)
                    .filter(path -> !Files.isDirectory(path))
                    .filter(path -> !path.equals(zipFile)) // 排除ZIP文件本身
                    .forEach(path -> {
                        try {
                            String entryName = sourceDir.relativize(path).toString();
                            ZipEntry entry = new ZipEntry(entryName);
                            zos.putNextEntry(entry);
                            Files.copy(path, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            log.error("添加文件到ZIP失败: {}", path, e);
                        }
                    });
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectoryRecursively(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }

    @Override
    public String generateAudioZipAndUpload(List<String> textbookItemIds, Textbook textbook) {
        try {
            String textbookId = textbook.getId();
            String textbookName = textbook.getName();
            // 生成音频ZIP文件
            byte[] zipBytes = generateAudioZip(textbookItemIds, textbook);

            // 构建OSS对象名称
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sanitizedName = sanitizeFileName(textbookName);
            String objectName = String.format("textbook-audio/%s/%s_%s_音频.zip",
                    sanitizedName, sanitizedName, timestamp);

            // 设置文件元数据
            String fileName = String.format("%s_音频.zip", textbookName);
            ObjectMetadata metadata = OssService.newFilenameMetadata(fileName);
            metadata.setContentType("application/zip");

            // 上传到OSS
            String ossUrl = ossService.uploadBytes(zipBytes, objectName, metadata);

            log.info("音频ZIP文件上传成功: {}", ossUrl);
            return ossUrl;

        } catch (Exception e) {
            log.error("生成并上传音频ZIP文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成并上传音频ZIP文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ByteArrayOutputStream generateExercisePdf(List<String> textbookItemIds, Textbook textbook, String pdfName) {
        try {
            log.info("开始生成教材练习PDF, 教材: {}, 单词项数量: {}", textbook.getName(), textbookItemIds.size());

            // 构建错误单词的课程环节数据（用于练习生成）
            List<CourseSectionDto> sections = buildSectionsFromTextbookItems(textbookItemIds);

            if (sections.isEmpty()) {
                log.warn("没有找到有效的单词数据，无法生成练习PDF");
                return null;
            }

            // 直接复用现有的课堂练习生成逻辑
            String title1 = "1. 看音标，拼读一遍，写出单词（尽自己能力写，不确定的可以看讲义，但是要用红笔进行订正，目的是找到自己的记忆重点），然后复习中文释义，再写一遍单词加深记忆。如果有错误，可再订正3遍。";
            String title2 = "2. 看句子翻译，对照讲义抄写1遍英文句子（也可以直接默写，然后红笔订正，找到记忆重点）。";

            // 调用CourseService的练习生成方法
            return courseService.generatePracticesInfoPdf(sections, null, title1, title2, pdfName, true);

        } catch (Exception e) {
            log.error("生成教材练习PDF失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据教材项ID列表构建课程环节数据
     */
    private List<CourseSectionDto> buildSectionsFromTextbookItems(List<String> textbookItemIds) {
        // 获取单词信息
        List<CourseSectionWordInfo> wordInfos = courseService.buildWords(textbookItemIds, true);

        if (wordInfos.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建课程环节
        CourseSectionDto section = new CourseSectionDto();
        section.setType("学习");
        section.setWords(wordInfos.stream().map(wordInfo -> {
            CourseSectionWordDto wordDto = new CourseSectionWordDto();
            wordDto.setWordInfo(wordInfo);
            return wordDto;
        }).collect(Collectors.toList()));

        return List.of(section);
    }

    public static void main(String[] args) {
        String x = "冬天； 冬季；冬季\n";
        System.out.println(Arrays.stream(x.split("[；;，,\b\n]")).map(String::trim).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.joining("；")));
    }
}
